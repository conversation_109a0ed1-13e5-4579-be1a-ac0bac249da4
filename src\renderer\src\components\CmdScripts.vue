<template>
  <div class="cmd-scripts-container">
    <div class="script-list-panel">
      <div class="panel-header">
        <h3>CMD 脚本列表</h3>
        <button @click="openAddModal" class="add-btn">新增脚本</button>
      </div>
      <ul> 
        <li
          v-for="script in scripts"
          :key="script.id"
          @click="selectScript(script)"
          :class="{ active: selectedScript && selectedScript.id === script.id }"
        >
          <div class="script-info">
            <span class="script-name">{{ script.name }}</span>
            <div class="script-indicators">
              <span
                v-if="script.is_system_builtin"
                class="builtin-indicator"
                title="系统内置脚本"
              >
                🔒 内置
              </span>
              <span
                v-if="runningScripts.includes(script.id)"
                class="running-indicator"
                title="脚本正在运行"
              >
                🟢 运行中
              </span>
            </div>
          </div>
          <button
            @click.stop="deleteScript(script)"
            :disabled="script.is_system_builtin"
            :class="{ 'disabled': script.is_system_builtin }"
            class="delete-btn"
            :title="script.is_system_builtin ? '系统内置脚本不能删除' : '删除脚本'"
          >
            删除
          </button>
        </li>
      </ul>
    </div>
    <div class="script-editor-panel" v-if="selectedScript">
      <div class="editor-header">
        <input type="text" v-model="selectedScript.name" class="title-input" readonly />
        <div class="button-group">
          <button
            @click="runScript"
            :disabled="isScriptRunning"
            class="run-btn"
            :class="{ 'disabled': isScriptRunning }"
          >
            {{ isScriptRunning ? '运行中...' : '运行' }}
          </button>
          <button
            @click="stopScript"
            v-if="isScriptRunning"
            class="stop-btn"
          >
            停止
          </button>
          <button @click="saveScript" :disabled="!isDirty" class="save-btn">
            {{ isDirty ? '保存' : '已保存' }}
          </button>
        </div>
      </div>
      <p class="script-description">{{ selectedScript.description }}</p>
      <textarea v-model="editedContent" class="code-editor"></textarea>

      <!-- 系统内置设置 -->
      <div class="system-builtin-config">
        <div class="config-row">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="isSystemBuiltin"
              @change="updateSystemBuiltin"
              class="builtin-checkbox"
            />
            <span class="checkbox-text">系统内置脚本</span>
            <span class="checkbox-description">勾选后此脚本将不能被删除</span>
          </label>
        </div>
      </div>

      <!-- 自启动配置 -->
      <div class="auto-start-config">
        <h4>自启动配置</h4>
        <div class="config-row">
          <label>
            <input 
              type="checkbox" 
              v-model="autoStartEnabled"
              @change="toggleAutoStart"
              :disabled="isTogglingAutoStart"
            />
            {{ autoStartEnabled ? '取消自启动' : '设为自启动' }}
          </label>
          <span v-if="isTogglingAutoStart" class="loading">处理中...</span>
        </div>
        <div class="config-row" v-if="autoStartEnabled">
          <label>启动延迟（秒）：</label>
          <input 
            type="number" 
            v-model.number="autoStartDelay"
            @change="updateAutoStartDelay"
            min="0" 
            max="300"
            class="delay-input"
          />
        </div>
      </div>
    </div>
    <div v-else class="placeholder">
      <p>请从左侧选择一个脚本进行编辑，或新增一个脚本。</p>
    </div>

    <!-- Add Script Modal -->
    <div v-if="showAddModal" class="modal-overlay" @click.self="closeAddModal">
      <div class="modal-content">
        <h3>新增 CMD 脚本</h3>
        <input
          type="text"
          v-model="newScriptName"
          placeholder="脚本名称"
          ref="newScriptNameInput"
          @keyup.enter="addScript"
        />
        <textarea v-model="newScriptDescription" placeholder="脚本描述"></textarea>
        <div class="modal-actions">
          <button @click="closeAddModal">取消</button>
          <button @click="addScript">确认</button>
        </div>
      </div>
    </div>

    <!-- Logs Modal -->
    <div v-if="showLogsModal" class="modal-overlay" @click.self="closeLogsModal">
      <div class="modal-content logs-modal">
        <h3>“{{ selectedScript.name }}” 的运行日志</h3>
        <div v-if="logs.length" class="logs-container">
          <ul>
            <li v-for="log in logs" :key="log.id">
              <p><strong>时间:</strong> {{ new Date(log.timestamp).toLocaleString() }}</p>
              <pre>{{ log.log_content }}</pre>
            </li>
          </ul>
        </div>
        <div v-else>
          <p>暂无日志记录。</p>
        </div>
        <div class="modal-actions">
          <button @click="closeLogsModal">关闭</button>
        </div>
      </div>
    </div>

    <!-- 自定义弹窗 -->
    <CustomDialog
      :visible="dialog.visible"
      :type="dialog.type"
      :title="dialog.title"
      :message="dialog.message"
      :confirmText="dialog.confirmText"
      :isDangerous="dialog.isDangerous"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
      @close="handleDialogClose"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, inject } from 'vue'
import CustomDialog from './CustomDialog.vue'
import { pageStateManager } from '../utils/pageStateManager.js'

const scripts = ref([])
const selectedScript = ref(null)
const editedContent = ref('')
const isDirty = ref(false)

const showAddModal = ref(false)
const newScriptName = ref('')
const newScriptDescription = ref('')
const newScriptNameInput = ref(null)

const showLogsModal = ref(false)
const logs = ref([])

// 自启动配置
const autoStartEnabled = ref(false)
const autoStartDelay = ref(0)
const autoStartConfig = ref(null)
const isTogglingAutoStart = ref(false)

// 进程管理
const runningScripts = ref([]) // 正在运行的脚本ID列表
const isScriptRunning = ref(false) // 当前选中脚本是否正在运行
const processCheckInterval = ref(null) // 定时检查进程状态的定时器

// 系统内置设置
const isSystemBuiltin = ref(false) // 当前选中脚本是否为系统内置

// 自定义弹窗状态
const dialog = ref({
  visible: false,
  type: 'info',
  title: '提示',
  message: '',
  confirmText: '确定',
  isDangerous: false,
  onConfirm: null,
  onCancel: null
})

// 页面状态管理
const pageId = 'CmdScripts'
// 移除inject依赖，直接使用pageStateManager

// 状态持久化
function savePageState() {
  const state = {
    selectedScriptId: selectedScript.value?.id || null,
    editedContent: editedContent.value,
    isDirty: isDirty.value,
    autoStartEnabled: autoStartEnabled.value,
    autoStartDelay: autoStartDelay.value,
    runningScripts: runningScripts.value,
    scrollPosition: document.querySelector('.script-list-panel ul')?.scrollTop || 0
  }
  
  pageStateManager.savePageState(pageId, state)
  console.log(`[${pageId}] 状态已保存:`, state)
}

function restorePageState() {
  const state = pageStateManager.getPageState(pageId)
  if (!state) {
    console.log(`[${pageId}] 没有找到保存的状态`)
    return
  }
  
  console.log(`[${pageId}] 恢复状态:`, state)
  
  // 恢复选中的脚本
  if (state.selectedScriptId && scripts.value.length > 0) {
    const script = scripts.value.find(s => s.id === state.selectedScriptId)
    if (script) {
      selectedScript.value = script
      editedContent.value = state.editedContent || script.content
      isDirty.value = state.isDirty || false
      autoStartEnabled.value = state.autoStartEnabled || false
      autoStartDelay.value = state.autoStartDelay || 0
    }
  }
  
  // 恢复运行状态
  if (state.runningScripts) {
    runningScripts.value = state.runningScripts
  }
  
  // 恢复滚动位置
  if (state.scrollPosition) {
    nextTick(() => {
      const listElement = document.querySelector('.script-list-panel ul')
      if (listElement) {
        listElement.scrollTop = state.scrollPosition
      }
    })
  }
}

// 监听状态变化并自动保存
watch([selectedScript, editedContent, isDirty, autoStartEnabled, autoStartDelay, runningScripts], () => {
  savePageState()
}, { deep: true })

// 自定义弹窗函数
function showDialog(options) {
  dialog.value = {
    visible: true,
    type: options.type || 'info',
    title: options.title || '提示',
    message: options.message || '',
    confirmText: options.confirmText || '确定',
    isDangerous: options.isDangerous || false,
    onConfirm: options.onConfirm || null,
    onCancel: options.onCancel || null
  }
}

function showAlert(message, type = 'info', title = '提示') {
  return new Promise((resolve) => {
    showDialog({
      type,
      title,
      message,
      onConfirm: resolve,
      onCancel: resolve
    })
  })
}

function showConfirm(message, title = '确认', isDangerous = false) {
  return new Promise((resolve) => {
    showDialog({
      type: 'confirm',
      title,
      message,
      isDangerous,
      confirmText: isDangerous ? '删除' : '确定',
      onConfirm: () => resolve(true),
      onCancel: () => resolve(false)
    })
  })
}

function handleDialogConfirm() {
  if (dialog.value.onConfirm) {
    dialog.value.onConfirm()
  }
  dialog.value.visible = false
}

function handleDialogCancel() {
  if (dialog.value.onCancel) {
    dialog.value.onCancel()
  }
  dialog.value.visible = false
}

function handleDialogClose() {
  dialog.value.visible = false
}

async function fetchScripts() {
  scripts.value = await window.api.getCmdScripts()
}

onMounted(async () => {
  await fetchScripts()
  // 初始化运行脚本列表
  await updateRunningScripts()

  // 恢复页面状态
  restorePageState()

  // 启动定时检查进程状态（每5秒检查一次）
  processCheckInterval.value = setInterval(async () => {
    await updateRunningScripts()
  }, 5000)

  console.log(`[${pageId}] 组件已挂载并恢复状态`)
})

onUnmounted(() => {
  // 清理定时器
  if (processCheckInterval.value) {
    clearInterval(processCheckInterval.value)
    processCheckInterval.value = null
  }
})

watch(selectedScript, (newVal) => {
  if (newVal) {
    editedContent.value = newVal.content
    isDirty.value = false
  } else {
    editedContent.value = ''
  }
})

watch(editedContent, (newVal) => {
  if (selectedScript.value) {
    isDirty.value = newVal !== selectedScript.value.content
  }
})

async function selectScript(script) {
  if (isDirty.value) {
    const confirmed = await showConfirm('当前脚本有未保存的更改，确定要切换吗？', '确认切换', true)
    if (!confirmed) {
      return
    }
  }
  selectedScript.value = script

  // 加载自启动配置和运行状态
  if (script) {
    await loadAutoStartConfig()
    // 检查脚本运行状态
    isScriptRunning.value = await checkScriptRunning(script.id)
    // 加载系统内置状态
    isSystemBuiltin.value = Boolean(script.is_system_builtin)
  } else {
    isScriptRunning.value = false
    isSystemBuiltin.value = false
  }
}

async function saveScript() {
  if (!selectedScript.value || !isDirty.value) return
  await window.api.updateCmdScript({
    id: selectedScript.value.id,
    content: editedContent.value
  })
  selectedScript.value.content = editedContent.value
  isDirty.value = false
}

async function openAddModal() {
  showAddModal.value = true
  // 等待DOM更新后聚焦到输入框
  await nextTick()
  if (newScriptNameInput.value) {
    newScriptNameInput.value.focus()
  }
}

function closeAddModal() {
  showAddModal.value = false
  newScriptName.value = ''
  newScriptDescription.value = ''
  // 清除输入框引用
  if (newScriptNameInput.value) {
    newScriptNameInput.value.blur()
  }
}

async function addScript() {
  if (!newScriptName.value.trim()) {
    await showAlert('脚本名称不能为空', 'warning')
    return
  }
  try {
    const newScript = await window.api.addCmdScript({
      name: newScriptName.value,
      description: newScriptDescription.value
    })

    if (newScript && newScript.id) {
      await fetchScripts()
      const scriptInList = scripts.value.find((s) => s.id === newScript.id)
      if (scriptInList) {
        selectScript(scriptInList)
      }
      closeAddModal()
    } else {
      await showAlert('添加脚本失败：从后端未收到有效的新脚本信息。', 'error')
      console.error('Failed to add script, invalid response:', newScript)
    }
  } catch (error) {
    console.error('添加脚本时出错:', error)
    await showAlert(`添加脚本时出错: ${error.message}\n请检查开发者工具(Ctrl+Shift+I)中的控制台以获取详细信息。`, 'error')
  }
}

async function deleteScript(script) {
  // 检查是否为系统内置脚本
  if (script.is_system_builtin) {
    await showAlert('系统内置脚本不能删除', 'warning')
    return
  }

  const confirmed = await showConfirm(`确定要删除脚本 “${script.name}” 吗？`, '删除脚本', true)
  if (confirmed) {
    try {
      await window.api.deleteCmdScript(script.id)
      if (selectedScript.value && selectedScript.value.id === script.id) {
        selectedScript.value = null
        isSystemBuiltin.value = false
      }
      await fetchScripts()
      await showAlert(`脚本 "${script.name}" 已成功删除`, 'success')
    } catch (error) {
      console.error('删除脚本时出错:', error)
      await showAlert(`删除脚本失败: ${error.message}`, 'error')
    }
  }
}

async function runScript() {
  if (!selectedScript.value) return

  try {
    // 1. 检查脚本是否已经在运行
    const runningCheck = await window.api.checkCmdScriptRunning(selectedScript.value.id)
    if (runningCheck.isRunning) {
      await showAlert('脚本已在运行中，请先停止当前运行的脚本', 'warning')
      return
    }

    // 2. Auto-save if dirty, without confirmation
    if (isDirty.value) {
      await saveScript()
      console.log('Script auto-saved before running.')
    }

    // 3. 显示运行状态
    console.log('开始通过新窗口执行CMD脚本:', selectedScript.value.name)

    // 4. 使用新窗口方式运行脚本
    const result = await window.api.runCmdScriptInWindow({
      id: selectedScript.value.id,
      content: selectedScript.value.content
    })

    console.log('CMD脚本已在新窗口启动:', result)

    // 5. 处理结果
    if (result.success) {
      // 脚本启动成功，更新运行状态，不显示提示
      await updateRunningScripts()
      console.log('✅ CMD脚本启动成功:', selectedScript.value.name)
    } else {
      if (result.alreadyRunning) {
        await showAlert('脚本已在运行中，请先停止当前运行的脚本', 'warning')
      } else {
        await showAlert(`启动CMD窗口失败: ${result.message || '未知错误'}`, 'error')
      }
    }

  } catch (error) {
    console.error('启动CMD窗口时出错:', error)
    await showAlert(`启动CMD窗口失败: ${error.message}`, 'error')
  }
}

// 停止脚本
async function stopScript() {
  if (!selectedScript.value) return

  try {
    console.log('停止CMD脚本:', selectedScript.value.name)

    const result = await window.api.stopCmdScript(selectedScript.value.id)

    if (result.success) {
      await showAlert('脚本已停止', 'success')
      await updateRunningScripts()
    } else {
      await showAlert(`停止脚本失败: ${result.message || '未知错误'}`, 'error')
    }
  } catch (error) {
    console.error('停止脚本时出错:', error)
    await showAlert(`停止脚本失败: ${error.message}`, 'error')
  }
}

// 更新正在运行的脚本列表
async function updateRunningScripts() {
  try {
    const result = await window.api.getRunningCmdScripts()
    if (result.success) {
      runningScripts.value = result.scripts.map(script => script.scriptId)

      // 更新当前选中脚本的运行状态
      if (selectedScript.value) {
        isScriptRunning.value = runningScripts.value.includes(selectedScript.value.id)
      }
    }
  } catch (error) {
    console.error('更新运行脚本列表时出错:', error)
  }
}

// 检查单个脚本的运行状态
async function checkScriptRunning(scriptId) {
  try {
    const result = await window.api.checkCmdScriptRunning(scriptId)
    return result.isRunning
  } catch (error) {
    console.error('检查脚本运行状态时出错:', error)
    return false
  }
}

// 更新系统内置状态
async function updateSystemBuiltin() {
  if (!selectedScript.value) return

  try {
    await window.api.updateCmdScriptBuiltin({
      id: selectedScript.value.id,
      isSystemBuiltin: isSystemBuiltin.value
    })

    // 更新本地脚本对象
    selectedScript.value.is_system_builtin = isSystemBuiltin.value

    // 更新脚本列表中的对应项
    const scriptIndex = scripts.value.findIndex(s => s.id === selectedScript.value.id)
    if (scriptIndex !== -1) {
      scripts.value[scriptIndex].is_system_builtin = isSystemBuiltin.value
    }

    await showAlert(
      isSystemBuiltin.value ? '已设置为系统内置脚本' : '已取消系统内置设置',
      'success'
    )
  } catch (error) {
    console.error('更新系统内置状态时出错:', error)
    // 恢复原状态
    isSystemBuiltin.value = Boolean(selectedScript.value.is_system_builtin)
    await showAlert(`更新系统内置状态失败: ${error.message}`, 'error')
  }
}

async function showLogs() {
  if (!selectedScript.value) return
  logs.value = await window.api.getScriptLogs(selectedScript.value.id)
  showLogsModal.value = true
}

function closeLogsModal() {
  showLogsModal.value = false
  logs.value = []
}

// 自启动配置相关函数
async function loadAutoStartConfig() {
  if (!selectedScript.value) return
  
  try {
    const config = await window.api.autoStart.checkScriptExists('cmd', selectedScript.value.name)
    if (config) {
      autoStartConfig.value = config
      autoStartEnabled.value = Boolean(config.enabled)
      autoStartDelay.value = config.delay_seconds || 0
    } else {
      autoStartConfig.value = null
      autoStartEnabled.value = false
      autoStartDelay.value = 0
    }
  } catch (error) {
    console.error('加载自启动配置失败:', error)
    autoStartConfig.value = null
    autoStartEnabled.value = false
    autoStartDelay.value = 0
  }
}

async function toggleAutoStart() {
  if (!selectedScript.value) return
  
  isTogglingAutoStart.value = true
  try {
    if (autoStartEnabled.value) {
      // 添加自启动
      await window.api.autoStart.addScript('cmd', selectedScript.value.name, autoStartDelay.value)
      console.log('CMD脚本已设为自启动:', selectedScript.value.name)
    } else {
      // 取消自启动
      await window.api.autoStart.deleteScript(autoStartConfig.value.id)
      console.log('CMD脚本已取消自启动:', selectedScript.value.name)
    }
  } catch (error) {
    console.error('更新自启动配置失败:', error)
    // 恢复状态
    autoStartEnabled.value = !autoStartEnabled.value
    await showAlert('更新自启动配置失败: ' + error.message, 'error')
  } finally {
    isTogglingAutoStart.value = false
  }
}

async function updateAutoStartDelay() {
  if (!selectedScript.value || !autoStartEnabled.value || !autoStartConfig.value) return
  
  try {
    await window.api.autoStart.updateScript(autoStartConfig.value.id, autoStartEnabled.value, autoStartDelay.value)
    console.log('CMD脚本自启动延迟已更新:', autoStartDelay.value + '秒')
  } catch (error) {
    console.error('更新自启动延迟失败:', error)
    await showAlert('更新自启动延迟失败: ' + error.message, 'error')
  }
}
</script>

<style scoped>
.cmd-scripts-container {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: var(--background);
  color: var(--text-primary);
  overflow: hidden;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

.script-list-panel {
  width: 280px;
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  background-color: var(--background-secondary);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.panel-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--background-tertiary);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.panel-header h3 {
  margin: 0;
  color: var(--text-primary);
  transition: color var(--transition-normal);
}

.add-btn {
  background-color: var(--success);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all var(--transition-fast);
}
.add-btn:hover {
  background-color: var(--success);
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.script-list-panel ul {
  list-style: none;
  padding: 0.5rem;
  margin: 0;
  overflow-y: auto;
  flex-grow: 1;
}

.script-list-panel li {
  padding: 0.8rem 1rem;
  margin-bottom: 0.3rem;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.script-list-panel li:hover {
  background-color: var(--card-hover);
  color: var(--text-primary);
}

.script-list-panel li.active {
  background-color: var(--primary);
  color: white;
  font-weight: 500;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

.delete-btn {
  background-color: transparent;
  color: var(--error);
  border: none;
  cursor: pointer;
  visibility: hidden;
  opacity: 0;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all var(--transition-fast);
}

.delete-btn:hover {
  background-color: var(--error);
  color: white;
}

.script-list-panel li:hover .delete-btn {
  visibility: visible;
  opacity: 1;
}

/* 脚本信息和运行状态 */
.script-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.script-name {
  flex: 1;
}

.script-indicators {
  display: flex;
  align-items: center;
  gap: 6px;
}

.running-indicator {
  font-size: 12px;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}

.builtin-indicator {
  font-size: 12px;
  color: #fa8c16;
  background: rgba(250, 140, 22, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}


.script-editor-panel {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background-color: var(--background);
  transition: background-color var(--transition-normal);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.title-input {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: bold;
  transition: color var(--transition-normal);
}

.button-group button {
  margin-left: 0.5rem;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}
.log-btn {
  background-color: var(--info);
  color: white;
}
.log-btn:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}
.run-btn {
  background-color: var(--warning);
  color: white;
}
.run-btn:hover:not(.disabled) {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.run-btn.disabled {
  background-color: var(--background-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.stop-btn {
  background-color: var(--error);
  color: white;
}

.stop-btn:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

/* 系统内置配置 */
.system-builtin-config {
  margin: 16px 0;
  padding: 12px;
  background: var(--background-secondary);
  border-radius: 8px;
  border-left: 4px solid var(--warning);
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.builtin-checkbox {
  margin-top: 2px;
}

.checkbox-text {
  font-weight: 500;
  color: var(--text-primary);
}

.checkbox-description {
  color: var(--text-secondary);
  font-size: 12px;
  margin-left: 8px;
}

.delete-btn.disabled {
  background-color: var(--background-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.6;
}

.delete-btn.disabled:hover {
  background-color: var(--background-tertiary);
  transform: none;
}
.save-btn {
  background-color: var(--primary);
  color: white;
}
.save-btn:hover:not(:disabled) {
  filter: brightness(1.1);
  transform: translateY(-1px);
}
.save-btn:disabled {
  background-color: var(--background-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.script-description {
  color: var(--text-tertiary);
  margin-bottom: 1rem;
  font-style: italic;
  transition: color var(--transition-normal);
}

.code-editor {
  flex-grow: 1;
  background-color: var(--input-background);
  color: var(--text-primary);
  border: 1px solid var(--input-border);
  border-radius: 4px;
  padding: 1rem;
  font-family: 'Courier New', Courier, monospace;
  font-size: 1rem;
  resize: none;
  transition: all var(--transition-normal);
}

.code-editor:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  color: var(--text-quaternary);
  background-color: var(--background);
  transition: all var(--transition-normal);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--background-secondary);
  padding: 2rem;
  border-radius: 8px;
  width: 400px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border: 1px solid var(--border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all var(--transition-normal);
}

.modal-content h3 {
  margin-top: 0;
  color: var(--text-primary);
  transition: color var(--transition-normal);
}

.modal-content input,
.modal-content textarea {
  width: 100%;
  padding: 0.5rem;
  background-color: var(--input-background);
  border: 1px solid var(--input-border);
  color: var(--text-primary);
  border-radius: 4px;
  transition: all var(--transition-normal);
  box-sizing: border-box;
}

.modal-content input:focus,
.modal-content textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.modal-content textarea {
  min-height: 100px;
  resize: vertical;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
.modal-actions button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}
.modal-actions button:first-child {
  background-color: var(--button-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border);
}
.modal-actions button:first-child:hover {
  background-color: var(--button-secondary-hover);
  color: var(--text-primary);
}
.modal-actions button:last-child {
  background-color: var(--primary);
  color: white;
}
.modal-actions button:last-child:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.logs-modal {
  width: 70%;
  max-width: 800px;
  height: 70%;
}

.logs-container {
  flex-grow: 1;
  overflow-y: auto;
  background-color: #1e1e1e;
  padding: 1rem;
  border-radius: 4px;
}
.logs-container ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.logs-container li {
  border-bottom: 1px solid #444;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}
.logs-container pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #252525;
  padding: 0.5rem;
  border-radius: 4px;
  color: #ddd;
}

/* 自启动配置样式 */
.auto-start-config {
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 6px;
  transition: all var(--transition-normal);
}

.auto-start-config h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.config-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.config-row:last-child {
  margin-bottom: 0;
}

.config-row label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.config-row input[type="checkbox"] {
  accent-color: var(--primary);
}

.delay-input {
  width: 80px;
  padding: 4px 8px;
  background-color: var(--input-background);
  border: 1px solid var(--input-border);
  border-radius: 4px;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.delay-input:focus {
  outline: none;
  border-color: var(--primary);
}

.loading {
  color: var(--text-tertiary);
  font-size: 0.8rem;
}
</style>