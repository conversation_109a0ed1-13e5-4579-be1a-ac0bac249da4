import { ipcMain, app } from 'electron'
import { open } from 'sqlite'
import sqlite3 from 'sqlite3'
import { join, dirname } from 'path'
import path from 'path'
import { fileURLToPath } from 'url'
import fs from 'fs-extra'
import { exec } from 'child_process'
import iconv from 'iconv-lite'
import { is } from '@electron-toolkit/utils'
import os from 'os'
import { backgroundProcessManager } from './background-process-manager.js'

const __dirname = dirname(fileURLToPath(import.meta.url))

// 记录应用启动时间
const APP_START_TIME = Date.now()

// Get the correct database path for both development and production
let dbPath
if (is.dev) {
  // Development: use resources directory in project root
  dbPath = join(__dirname, '../../resources/database.db')
} else {
  // Production: use the same path as main process
  const exeDir = dirname(app.getPath('exe'))
  dbPath = join(exeDir, 'resources', 'app.asar.unpacked', 'resources', 'database.db')
}

console.log('Database path:', dbPath)

let db

async function initializeDatabase() {
  try {
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    })

    await db.exec(`
      CREATE TABLE IF NOT EXISTS cmd_scripts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        content TEXT
      );
    `)

    await db.exec(`
      CREATE TABLE IF NOT EXISTS script_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        script_id INTEGER,
        log_content TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (script_id) REFERENCES cmd_scripts (id) ON DELETE CASCADE
      );
    `)

    await db.exec(`
      CREATE TABLE IF NOT EXISTS python_script_configs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          script_name TEXT UNIQUE NOT NULL,
          config_schema TEXT,
          config_values TEXT
      );
    `)

    await db.exec(`
      CREATE TABLE IF NOT EXISTS user_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT UNIQUE NOT NULL,
          value TEXT,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `)
  } catch (err) {
    console.error('Failed to initialize database:', err)
  }
}

initializeDatabase()

// CMD Scripts IPC Handlers
ipcMain.handle('db:get-cmd-scripts', async () => {
  return await db.all('SELECT * FROM cmd_scripts')
})

ipcMain.handle('db:add-cmd-script', async (event, data) => {
  const { name, description = '' } = data
  const result = await db.run('INSERT INTO cmd_scripts (name, description, content) VALUES (?, ?, ?)', name, description, '')
  return { id: result.lastID, name, description, content: '' }
})

ipcMain.handle('db:update-cmd-script', async (event, { id, content }) => {
  return await db.run('UPDATE cmd_scripts SET content = ? WHERE id = ?', content, id)
})

ipcMain.handle('db:delete-cmd-script', async (event, id) => {
  // 检查脚本是否为系统内置
  const script = await db.get('SELECT is_system_builtin FROM cmd_scripts WHERE id = ?', id)
  if (script && script.is_system_builtin) {
    throw new Error('系统内置脚本不能删除')
  }
  return await db.run('DELETE FROM cmd_scripts WHERE id = ?', id)
})

ipcMain.handle('db:update-cmd-script-builtin', async (event, { id, isSystemBuiltin }) => {
  return await db.run('UPDATE cmd_scripts SET is_system_builtin = ? WHERE id = ?', isSystemBuiltin ? 1 : 0, id)
})

// 引入新的CMD脚本运行器
import { cmdScriptRunner } from './cmd-script-runner.js'

// 新增：通过bash打开cmd窗口直接执行脚本
ipcMain.handle('cmd:run-script-in-window', async (event, { id, content }) => {
  try {
    console.log(`[IPC] CMD脚本新窗口执行请求: ${id}`);

    const result = await cmdScriptRunner.runScriptInNewWindow(id, content, {
      cwd: process.cwd(),
      env: { ...process.env }
    });

    console.log(`[IPC] CMD脚本已在新窗口启动: ${id}`);
    return result;

  } catch (error) {
    console.error('[IPC] CMD脚本新窗口执行异常:', error);
    throw new Error(`启动cmd窗口失败: ${error.message}`);
  }
});

ipcMain.handle('cmd:run-script', async (event, { id, content }) => {
  try {
    console.log(`[IPC] CMD脚本执行请求: ${id}`);

    const result = await cmdScriptRunner.runScript(id, content, {
      timeout: 30000, // 30秒超时
      encoding: 'gbk'   // GBK编码处理中文
    });

    // 记录日志到数据库
    if (db) {
      const logOutput = `执行时间: ${new Date().toLocaleString()}\n` +
        `脚本ID: ${id}\n` +
        `执行结果: ${result.success ? '成功' : '失败'}\n` +
        `退出码: ${result.code}\n` +
        `执行耗时: ${result.executionTime || 0}ms\n` +
        `输出:\n${result.output}\n` +
        `${'='.repeat(50)}\n`;

      db.run('INSERT INTO script_logs (script_id, log_content) VALUES (?, ?)', id, logOutput)
        .catch(err => console.error('保存脚本日志失败:', err));
    }

    return result;

  } catch (error) {
    console.error('[IPC] CMD脚本执行异常:', error);
    throw new Error(`执行CMD脚本失败: ${error.message}`);
  }
})

ipcMain.handle('db:get-script-logs', async (event, scriptId) => {
  if (!db) {
    return []
  }
  return await db.all('SELECT * FROM script_logs WHERE script_id = ? ORDER BY timestamp DESC', scriptId)
})

// CMD脚本进程管理相关IPC处理器
ipcMain.handle('cmd:check-script-running', async (event, scriptId) => {
  try {
    const isRunning = cmdScriptRunner.isScriptRunning(scriptId);
    console.log(`[IPC] 检查脚本运行状态: ${scriptId}, 运行中: ${isRunning}`);
    return { isRunning };
  } catch (error) {
    console.error('[IPC] 检查脚本运行状态异常:', error);
    return { isRunning: false, error: error.message };
  }
});

ipcMain.handle('cmd:get-running-scripts', async () => {
  try {
    const runningScripts = cmdScriptRunner.getRunningScripts();
    console.log(`[IPC] 获取正在运行的脚本列表: ${runningScripts.length} 个脚本`);
    return { success: true, scripts: runningScripts };
  } catch (error) {
    console.error('[IPC] 获取正在运行的脚本列表异常:', error);
    return { success: false, error: error.message, scripts: [] };
  }
});

ipcMain.handle('cmd:stop-script', async (event, scriptId) => {
  try {
    console.log(`[IPC] 停止脚本请求: ${scriptId}`);
    const success = cmdScriptRunner.stopScript(scriptId);

    if (success) {
      console.log(`[IPC] 脚本已停止: ${scriptId}`);
      return { success: true, message: '脚本已停止' };
    } else {
      console.log(`[IPC] 脚本停止失败: ${scriptId} (脚本可能未在运行)`);
      return { success: false, message: '脚本未在运行或停止失败' };
    }
  } catch (error) {
    console.error('[IPC] 停止脚本异常:', error);
    return { success: false, error: error.message };
  }
});

// 新增：根据脚本名称执行CMD脚本的IPC处理器
ipcMain.handle('cmd:run-script-by-name', async (event, scriptName) => {
  try {
    console.log(`[IPC] 根据名称执行CMD脚本: ${scriptName}`);

    // 从数据库获取脚本内容
    const cmdScript = await db.get('SELECT * FROM cmd_scripts WHERE name = ?', scriptName);

    if (!cmdScript) {
      console.log(`[IPC] 未找到名为 "${scriptName}" 的CMD脚本`);
      return { success: false, message: `未找到名为 "${scriptName}" 的CMD脚本` };
    }

    // 创建临时脚本文件
    const tempDir = path.join(app.getPath('temp'), 'qingfeng_scripts');
    await fs.ensureDir(tempDir);
    const tempFilePath = path.join(tempDir, `service_${scriptName}_${Date.now()}.cmd`);

    // 使用GBK编码写入脚本内容以支持中文
    const gbkBuffer = iconv.encode(cmdScript.content, 'gbk');
    await fs.writeFile(tempFilePath, gbkBuffer);

    console.log(`[IPC] 临时脚本文件已创建: ${tempFilePath}`);

    // 在新的cmd窗口中执行脚本
    exec(`start "${scriptName}" cmd /k "${tempFilePath}"`, { shell: true }, (error) => {
      if (error) {
        console.error(`[IPC] 执行脚本 "${scriptName}" 时出错:`, error);
      } else {
        console.log(`[IPC] 脚本 "${scriptName}" 已在新窗口中启动`);
      }
    });

    return {
      success: true,
      message: `脚本 "${scriptName}" 已在新窗口中启动`,
      scriptId: cmdScript.id,
      scriptPath: tempFilePath
    };

  } catch (error) {
    console.error(`[IPC] 执行脚本 "${scriptName}" 异常:`, error);
    return { success: false, error: error.message };
  }
});

// Python Scripts IPC Handlers
// 使用与主进程相同的脚本路径逻辑
function getScriptsRoot() {
  if (is.dev) {
    // 开发环境：使用项目根目录
    return join(__dirname, '../../scripts')
  } else {
    // 生产环境：使用用户数据目录
    return join(app.getPath('userData'), 'scripts')
  }
}

const scriptsRoot = getScriptsRoot()
const pythonScriptsPath = join(scriptsRoot, 'python')

fs.ensureDirSync(pythonScriptsPath)

ipcMain.handle('python:get-scripts', async () => {
  try {
    const entries = await fs.readdir(pythonScriptsPath, { withFileTypes: true })
    const directories = entries.filter((dirent) => dirent.isDirectory()).map((dirent) => dirent.name)
    return directories
  } catch (error) {
    console.error('Failed to get Python scripts:', error)
    return []
  }
})

ipcMain.handle('python:add-script', async (event, name) => {
  const scriptDir = join(pythonScriptsPath, name)
  if (await fs.pathExists(scriptDir)) {
    throw new Error('Script with this name already exists.')
  }
  await fs.ensureDir(scriptDir)

  const defaultConfig = {
    schema: [
      { name: 'param1', label: '参数一', type: 'text', default: 'default_value' },
      { name: 'param2', label: '参数二', type: 'number', default: 100 }
    ]
  }
  const configPath = join(scriptDir, 'config.json')
  await fs.writeJson(configPath, defaultConfig, { spaces: 2 })

  const mainPyPath = join(scriptDir, 'main.py')
  const mainPyContent = `import sys
import json
import base64

def main():
    try:
        if len(sys.argv) > 1:
            # Decode the base64 string
            base64_config = sys.argv[1]
            decoded_config_str = base64.b64decode(base64_config).decode('utf-8')
            config = json.loads(decoded_config_str)
            print(f"Python script received config: {config}")
            
            # Your script logic here
            # For example, print one of the parameters
            print(f"Value of param1: {config.get('param1')}")
            
        else:
            print("No config provided.")
            
    except Exception as e:
        print(f"Error in python script: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
`
  await fs.writeFile(mainPyPath, mainPyContent)

  try {
    await db.run(
      'INSERT INTO python_script_configs (script_name, config_schema, config_values) VALUES (?, ?, ?)',
      name,
      JSON.stringify(defaultConfig.schema),
      JSON.stringify({})
    )
  } catch (dbError) {
    // If there's a DB error, we should roll back the file system changes
    await fs.remove(scriptDir)
    throw dbError
  }

  return { name }
})

ipcMain.handle('python:delete-script', async (event, name) => {
  // 检查脚本是否为系统内置
  const script = await db.get('SELECT is_system_builtin FROM python_scripts WHERE name = ?', name)
  if (script && script.is_system_builtin) {
    throw new Error('系统内置脚本不能删除')
  }

  const scriptDir = join(pythonScriptsPath, name)
  await fs.remove(scriptDir)
  await db.run('DELETE FROM python_script_configs WHERE script_name = ?', name)
  await db.run('DELETE FROM python_scripts WHERE name = ?', name)
  return { success: true }
})

ipcMain.handle('python:update-script-builtin', async (event, { name, isSystemBuiltin }) => {
  return await db.run('UPDATE python_scripts SET is_system_builtin = ? WHERE name = ?', isSystemBuiltin ? 1 : 0, name)
})

ipcMain.handle('python:get-script-config', async (event, scriptName) => {
  const scriptDir = join(pythonScriptsPath, scriptName)
  const configPath = join(scriptDir, 'config.json')

  let dbConfig = await db.get('SELECT * FROM python_script_configs WHERE script_name = ?', scriptName)

  if (!dbConfig && (await fs.pathExists(configPath))) {
    const fileConfig = await fs.readJson(configPath)
    const schema = JSON.stringify(fileConfig.schema || [])
    const values = JSON.stringify({})

    await db.run(
      'INSERT INTO python_script_configs (script_name, config_schema, config_values) VALUES (?, ?, ?)',
      scriptName,
      schema,
      values
    )
    dbConfig = { script_name: scriptName, config_schema: schema, config_values: values }
  }

  if (dbConfig) {
    return {
      schema: JSON.parse(dbConfig.config_schema || '[]'),
      values: JSON.parse(dbConfig.config_values || '{}')
    }
  }

  return null
})

ipcMain.handle('python:save-script-config', async (event, scriptName, configValues) => {
  await db.run('UPDATE python_script_configs SET config_values = ? WHERE script_name = ?', JSON.stringify(configValues), scriptName)
  return { success: true }
})

ipcMain.handle('python:run-script', async (event, { scriptName, configValues }) => {
  let finalConfigValues = configValues || {};

  // 如果configValues为空，从数据库获取配置
  if (!configValues || Object.keys(configValues).length === 0) {
    const configRow = await db.get('SELECT config_values FROM python_script_configs WHERE script_name = ?', scriptName)
    if (configRow) {
      finalConfigValues = JSON.parse(configRow.config_values || '{}')
    }
  }

  // Base64 encode the config to pass as a single, safe argument
  const configJson = JSON.stringify(finalConfigValues)
  const base64Config = Buffer.from(configJson).toString('base64')

  const scriptPath = join(pythonScriptsPath, scriptName, 'main.py')
  const command = `python "${scriptPath}" "${base64Config}"`

  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject({
          name: 'PythonExecutionError',
          message: `Error executing Python script: ${error.message}`,
          stdout,
          stderr
        });
      } else {
        resolve({ stdout, stderr });
      }
    });
  });
});









// Demo Audio Handler for Music Player
ipcMain.handle('get-demo-audio', async (event, filename) => {
  let audioPath;
  if (is.dev) {
    audioPath = join(__dirname, '../../resources/audio', filename);
  } else {
    const exeDir = dirname(app.getPath('exe'));
    audioPath = join(exeDir, 'resources', 'app.asar.unpacked', 'resources', 'audio', filename);
  }

  try {
    const buffer = await fs.readFile(audioPath);
    return `data:audio/wav;base64,${buffer.toString('base64')}`;
  } catch (error) {
    console.error('Failed to load demo audio:', error);
    throw new Error('Failed to load demo audio file');
  }
});

// System Stats API

// 添加日志控制变量
let lastStatsLogTime = 0;
const STATS_LOG_INTERVAL = 10000; // 10秒内只输出一次详细日志

ipcMain.handle('get-system-stats', async () => {
  try {
    const now = Date.now();
    const shouldLog = now - lastStatsLogTime > STATS_LOG_INTERVAL;
    
    if (shouldLog) {
      console.log('[System Stats] Collecting system statistics...');
      lastStatsLogTime = now;
    }

    // 获取CPU使用率
    const cpuUsage = await getCpuUsage();

    // 获取内存使用率
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsage = (usedMemory / totalMemory) * 100;

    // 获取磁盘使用率
    const diskUsage = await getDiskUsage();

    // 获取应用运行时间
    const appUptime = Math.floor((Date.now() - APP_START_TIME) / 1000);

    // 获取脚本总数
    const totalScripts = await getTotalScripts();

    // 获取定时任务数量
    const scheduledTasks = await getScheduledTasks();

    const result = {
      cpuUsage: Math.round(cpuUsage * 100) / 100,
      memoryUsage: Math.round(memoryUsage * 100) / 100,
      diskUsage: Math.round(diskUsage * 100) / 100,
      uptime: appUptime,
      totalScripts,
      scheduledTasks
    };

    if (shouldLog) {
      console.log('[System Stats] Result:', {
        cpu: `${result.cpuUsage}%`,
        memory: `${result.memoryUsage}%`,
        disk: `${result.diskUsage}%`,
        uptime: `${result.uptime}s`,
        scripts: result.totalScripts,
        tasks: result.scheduledTasks
      });
    }

    return result;
  } catch (error) {
    console.error('获取系统状态失败:', error);
    const appUptime = Math.floor((Date.now() - APP_START_TIME) / 1000);
    return {
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      uptime: appUptime,
      totalScripts: 0,
      scheduledTasks: 0
    };
  }
});

// 应用设置相关IPC处理
ipcMain.handle('settings:get', async (event, key) => {
  try {
    const result = await db.get('SELECT value FROM app_settings WHERE key = ?', key)
    return result ? result.value : null
  } catch (error) {
    console.error('获取设置失败:', error)
    return null
  }
})

ipcMain.handle('settings:set', async (event, key, value) => {
  try {
    await db.run('INSERT OR REPLACE INTO app_settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)', key, value)
    return { success: true }
  } catch (error) {
    console.error('保存设置失败:', error)
    return { success: false, error: error.message }
  }
})

ipcMain.handle('settings:get-all', async () => {
  try {
    const results = await db.all('SELECT key, value FROM app_settings')
    const settings = {}
    results.forEach(row => {
      settings[row.key] = row.value
    })
    return settings
  } catch (error) {
    console.error('获取所有设置失败:', error)
    return {}
  }
})

// 获取CPU使用率
async function getCpuUsage() {
  try {
    const os = require('os');
    const cpus = os.cpus();

    // 获取第一次CPU时间
    const startTimes = cpus.map(cpu => {
      const times = cpu.times;
      return {
        idle: times.idle,
        total: times.idle + times.user + times.nice + times.sys + times.irq
      };
    });

    // 等待100ms
    await new Promise(resolve => setTimeout(resolve, 100));

    // 获取第二次CPU时间
    const endCpus = os.cpus();
    const endTimes = endCpus.map(cpu => {
      const times = cpu.times;
      return {
        idle: times.idle,
        total: times.idle + times.user + times.nice + times.sys + times.irq
      };
    });

    // 计算CPU使用率
    let totalIdle = 0;
    let totalTick = 0;

    for (let i = 0; i < startTimes.length; i++) {
      const startIdle = startTimes[i].idle;
      const startTotal = startTimes[i].total;
      const endIdle = endTimes[i].idle;
      const endTotal = endTimes[i].total;

      const idle = endIdle - startIdle;
      const total = endTotal - startTotal;

      totalIdle += idle;
      totalTick += total;
    }

    const cpuUsage = totalTick === 0 ? 0 : Math.max(0, Math.min(100, 100 - (totalIdle / totalTick) * 100));
    return cpuUsage;
  } catch (error) {
    console.error('CPU使用率计算失败:', error);
    return 0;
  }
}

// 获取磁盘使用率
async function getDiskUsage() {
  return new Promise((resolve) => {
    try {
      const { exec } = require('child_process');

      if (process.platform === 'win32') {
        // Windows系统使用PowerShell命令
        exec('powershell "Get-WmiObject -Class Win32_LogicalDisk -Filter \\"DeviceID=\'C:\'\\" | Select-Object Size,FreeSpace | ConvertTo-Json"', (error, stdout) => {
          if (error) {
            console.error('Windows磁盘查询失败:', error);
            // 尝试备用方法
            exec('dir C:\\ /-c', (dirError, dirStdout) => {
              if (dirError) {
                resolve(75);
                return;
              }

              try {
                // 从dir命令输出中提取磁盘信息
                const lines = dirStdout.split('\n');
                const lastLine = lines[lines.length - 2] || '';
                const match = lastLine.match(/(\d+)\s+bytes\s+free/i);
                if (match) {
                  // 这是一个简化的估算，实际应用中可能需要更复杂的逻辑
                  resolve(80); // 估算值
                } else {
                  resolve(75);
                }
              } catch {
                resolve(75);
              }
            });
            return;
          }

          try {
            const diskInfo = JSON.parse(stdout);
            const totalSize = parseInt(diskInfo.Size) || 0;
            const freeSpace = parseInt(diskInfo.FreeSpace) || 0;

            if (totalSize > 0) {
              const usedSpace = totalSize - freeSpace;
              const usagePercent = (usedSpace / totalSize) * 100;
              resolve(Math.round(usagePercent));
            } else {
              resolve(75);
            }
          } catch (parseError) {
            console.error('Windows磁盘数据解析失败:', parseError);
            resolve(75);
          }
        });
      } else {
        // Linux/Mac系统使用df命令
        exec('df -h /', (error, stdout) => {
          if (error) {
            console.error('Unix磁盘查询失败:', error);
            resolve(75);
            return;
          }

          try {
            const lines = stdout.trim().split('\n');
            if (lines.length > 1) {
              const parts = lines[1].split(/\s+/);
              if (parts.length >= 5) {
                const usageStr = parts[4];
                const usage = parseInt(usageStr.replace('%', ''));
                resolve(isNaN(usage) ? 75 : usage);
              } else {
                resolve(75);
              }
            } else {
              resolve(75);
            }
          } catch (parseError) {
            console.error('Unix磁盘数据解析失败:', parseError);
            resolve(75);
          }
        });
      }
    } catch (error) {
      console.error('获取磁盘使用率失败:', error);
      resolve(75); // 默认值
    }
  });
}

// 获取脚本总数
async function getTotalScripts() {
  try {
    let totalCount = 0;

    // 获取CMD脚本数量
    const cmdScripts = await db.all('SELECT COUNT(*) as count FROM cmd_scripts');
    totalCount += cmdScripts[0]?.count || 0;

    // 获取JS脚本数量
    const jsScripts = await db.all('SELECT COUNT(*) as count FROM js_scripts');
    totalCount += jsScripts[0]?.count || 0;

    // 获取Python脚本数量（从文件系统）
    const fs = require('fs');
    const path = require('path');
    try {
      const pythonScriptsPath = path.join(getScriptsRoot(), 'python');
      if (fs.existsSync(pythonScriptsPath)) {
        const entries = await fs.promises.readdir(pythonScriptsPath, { withFileTypes: true });
        const pythonDirs = entries.filter(dirent => dirent.isDirectory());
        totalCount += pythonDirs.length;
      }
    } catch (pythonError) {
      // 静默处理Python脚本数量获取失败
    }

    return totalCount;
  } catch (error) {
    console.error('获取脚本总数失败:', error);
    return 0;
  }
}

// 获取定时任务数量
async function getScheduledTasks() {
  try {
    const result = await db.all('SELECT COUNT(*) as count FROM scheduled_tasks WHERE enabled = 1');
    return result[0]?.count || 0;
  } catch (error) {
    console.error('获取定时任务数量失败:', error);
    return 0;
  }
}

// 自启动脚本 IPC Handlers
ipcMain.handle('auto-start:get-scripts', async () => {
  try {
    return await db.all('SELECT * FROM auto_start_scripts ORDER BY created_at ASC')
  } catch (error) {
    console.error('获取自启动脚本失败:', error)
    throw error
  }
})

ipcMain.handle('auto-start:add-script', async (event, scriptType, scriptName, delaySeconds = 0) => {
  try {
    const result = await db.run(
      'INSERT INTO auto_start_scripts (script_type, script_name, enabled, delay_seconds) VALUES (?, ?, 1, ?)',
      scriptType,
      scriptName,
      delaySeconds
    )
    return { id: result.lastID, scriptType, scriptName, enabled: 1, delaySeconds }
  } catch (error) {
    console.error('添加自启动脚本失败:', error)
    throw error
  }
})

ipcMain.handle('auto-start:update-script', async (event, id, enabled, delaySeconds) => {
  try {
    await db.run(
      'UPDATE auto_start_scripts SET enabled = ?, delay_seconds = ? WHERE id = ?',
      enabled,
      delaySeconds,
      id
    )
    return { success: true }
  } catch (error) {
    console.error('更新自启动脚本失败:', error)
    throw error
  }
})

ipcMain.handle('auto-start:delete-script', async (event, id) => {
  try {
    await db.run('DELETE FROM auto_start_scripts WHERE id = ?', id)
    return { success: true }
  } catch (error) {
    console.error('删除自启动脚本失败:', error)
    throw error
  }
})

ipcMain.handle('auto-start:check-script-exists', async (event, scriptType, scriptName) => {
  try {
    const result = await db.get(
      'SELECT * FROM auto_start_scripts WHERE script_type = ? AND script_name = ?',
      scriptType,
      scriptName
    )
    return result || null
  } catch (error) {
    console.error('检查自启动脚本配置失败:', error)
    throw error
  }
})

// ==================== Background Process Management IPC Handlers ====================

// Start a new background process
ipcMain.handle('background-process:start', async (event, processConfig) => {
  try {
    console.log('[IPC] Starting background process:', processConfig.name)
    const processId = await backgroundProcessManager.startProcess(processConfig)
    return { success: true, processId }
  } catch (error) {
    console.error('[IPC] Failed to start background process:', error)
    return { success: false, error: error.message }
  }
})

// Update process progress
ipcMain.handle('background-process:update-progress', async (event, processId, progress, message) => {
  try {
    await backgroundProcessManager.updateProgress(processId, progress, message)
    return { success: true }
  } catch (error) {
    console.error('[IPC] Failed to update process progress:', error)
    return { success: false, error: error.message }
  }
})

// Complete a process
ipcMain.handle('background-process:complete', async (event, processId, output) => {
  try {
    await backgroundProcessManager.completeProcess(processId, output)
    return { success: true }
  } catch (error) {
    console.error('[IPC] Failed to complete process:', error)
    return { success: false, error: error.message }
  }
})

// Fail a process
ipcMain.handle('background-process:fail', async (event, processId, errorMessage) => {
  try {
    await backgroundProcessManager.failProcess(processId, errorMessage)
    return { success: true }
  } catch (error) {
    console.error('[IPC] Failed to fail process:', error)
    return { success: false, error: error.message }
  }
})

// Cancel a process
ipcMain.handle('background-process:cancel', async (event, processId) => {
  try {
    await backgroundProcessManager.cancelProcess(processId)
    return { success: true }
  } catch (error) {
    console.error('[IPC] Failed to cancel process:', error)
    return { success: false, error: error.message }
  }
})

// Get all active processes
ipcMain.handle('background-process:get-active', async (event) => {
  try {
    const processes = backgroundProcessManager.getActiveProcesses()
    return { success: true, processes }
  } catch (error) {
    console.error('[IPC] Failed to get active processes:', error)
    return { success: false, error: error.message, processes: [] }
  }
})

// Get all processes (active and completed)
ipcMain.handle('background-process:get-all', async (event) => {
  try {
    const processes = await backgroundProcessManager.getAllProcesses()
    return { success: true, processes }
  } catch (error) {
    console.error('[IPC] Failed to get all processes:', error)
    return { success: false, error: error.message, processes: [] }
  }
})

// Get process by ID
ipcMain.handle('background-process:get', async (event, processId) => {
  try {
    const process = backgroundProcessManager.getProcess(processId)
    return { success: true, process }
  } catch (error) {
    console.error('[IPC] Failed to get process:', error)
    return { success: false, error: error.message, process: null }
  }
})

// Delete a process
ipcMain.handle('background-process:delete', async (event, processId) => {
  try {
    await backgroundProcessManager.deleteProcess(processId)
    return { success: true }
  } catch (error) {
    console.error('[IPC] Failed to delete process:', error)
    return { success: false, error: error.message }
  }
})

// Clean up old processes
ipcMain.handle('background-process:cleanup', async (event, days = 7) => {
  try {
    await backgroundProcessManager.cleanupOldProcesses(days)
    return { success: true }
  } catch (error) {
    console.error('[IPC] Failed to cleanup old processes:', error)
    return { success: false, error: error.message }
  }
})

// Get process statistics
ipcMain.handle('background-process:stats', async (event) => {
  try {
    const stats = await backgroundProcessManager.getProcessStats()
    return { success: true, stats }
  } catch (error) {
    console.error('[IPC] Failed to get process stats:', error)
    return { success: false, error: error.message, stats: null }
  }
})

// Listen for process events and forward to renderer
backgroundProcessManager.on('processStarted', (processInfo) => {
  // Send to all renderer processes
  const { webContents } = require('electron')
  webContents.getAllWebContents().forEach(contents => {
    contents.send('background-process:started', processInfo)
  })
})

backgroundProcessManager.on('processProgress', (data) => {
  const { webContents } = require('electron')
  webContents.getAllWebContents().forEach(contents => {
    contents.send('background-process:progress', data)
  })
})

backgroundProcessManager.on('processFinished', (data) => {
  const { webContents } = require('electron')
  webContents.getAllWebContents().forEach(contents => {
    contents.send('background-process:finished', data)
  })
})

backgroundProcessManager.on('processDeleted', (processId) => {
  const { webContents } = require('electron')
  webContents.getAllWebContents().forEach(contents => {
    contents.send('background-process:deleted', processId)
  })
})