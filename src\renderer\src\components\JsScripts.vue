<template>
  <div class="js-scripts-container">
    <!-- Left Panel: Script List -->
    <div class="script-list-panel">
      <div class="panel-header">
        <h3>JS 脚本</h3>
        <button @click="openAddModal" class="add-btn">新增脚本</button>
      </div>
      <ul>
        <li
          v-for="script in scripts"
          :key="script.name"
          @click="selectScript(script)"
          :class="{ active: selectedScript && selectedScript.name === script.name }"
        >
          <span>{{ script.name }}</span>
          <button @click.stop="deleteScript(script)" class="delete-btn">删除</button>
        </li>
      </ul>
    </div>

    <!-- Right Panel: Dynamic Config Form & Log Output -->
    <div class="right-panel">
      <div class="script-config-panel" v-if="selectedScript">
        <div class="config-header">
          <input type="text" :value="selectedScript.name" class="title-input" readonly />
          <div class="button-group">
            <button @click="runScript" class="run-btn" :disabled="isRunning">
              {{ isRunning ? '运行中...' : '运行' }}
            </button>
            <button @click="saveConfig" :disabled="!isDirty || isRunning" class="save-btn">
              {{ isDirty ? '保存' : '已保存' }}
            </button>
            <button @click="testNodeEnvironment" class="test-btn" :disabled="isRunning">
              测试Node环境
            </button>
          </div>
        </div>

        <div class="config-section">
            <div class="config-header">
              <h4>配置参数</h4>
              <div class="config-actions">
                <button
                  @click="openAddConfigModal"
                  class="add-config-btn"
                  :disabled="isRunning"
                  ref="addConfigBtn"
                >
                  ➕ 添加配置项
                </button>
              </div>
            </div>

            <!-- 自启动配置 -->
            <div class="auto-start-config">
              <div class="config-header">
                <h4>自启动配置</h4>
                <div class="config-actions">
                  <button
                    @click="toggleAutoStart"
                    class="auto-start-btn"
                    :class="{ active: autoStartEnabled }"
                    :disabled="isRunning || isTogglingAutoStart"
                  >
                    {{ autoStartEnabled ? '取消自启动' : '设为自启动' }}
                  </button>
                </div>
              </div>
              <div v-if="autoStartEnabled || autoStartDelay !== null" class="auto-start-options">
                <div class="form-group">
                  <label>启动延迟 (秒)</label>
                  <input
                    type="number"
                    v-model.number="autoStartDelay"
                    min="0"
                    max="300"
                    :disabled="isRunning || isTogglingAutoStart"
                    @change="updateAutoStartDelay"
                  />
                </div>
              </div>
            </div>

          <div class="config-form" v-if="config.schema">
            <div v-for="(field, index) in config.schema" :key="field.name" class="form-group">
              <div class="form-group-header">
                <label :for="field.name">{{ field.label }}</label>
                <button
                  @click="promptDeleteConfigField(index)"
                  class="remove-field-btn"
                  :disabled="isRunning"
                >
                  🗑️
                </button>
              </div>

              <input
                v-if="field.type === 'text' || field.type === 'number'"
                :type="field.type"
                :id="field.name"
                v-model="config.values[field.name]"
              />

              <select
                v-else-if="field.type === 'select'"
                :id="field.name"
                v-model="config.values[field.name]"
              >
                <option v-for="option in field.options" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>

              <input
                v-else-if="field.type === 'checkbox'"
                type="checkbox"
                :id="field.name"
                v-model="config.values[field.name]"
                class="form-checkbox"
              />
            </div>

            <div v-if="config.schema.length === 0" class="no-config">
              <p>此脚本没有可配置的选项。</p>
              <p>点击"添加配置项"按钮来创建配置参数。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Log Output -->
      <div class="log-output-panel" v-if="selectedScript">
        <div class="log-header">
          <h4>运行日志</h4>
          <button @click="clearLog" class="clear-log-btn" :disabled="isRunning">清空日志</button>
        </div>
        <pre class="log-content" ref="logContentRef">{{ logOutput }}</pre>
      </div>

      <!-- Placeholder for when no script is selected -->
      <div v-if="!selectedScript" class="placeholder">
        <p>请从左侧选择一个脚本，或新增一个脚本。</p>
      </div>
    </div>

    <!-- Add Script Modal -->
    <div v-if="showAddModal" class="modal-overlay" @click.self="closeAddModal">
      <div class="modal-content">
        <h3>新增 JS 脚本</h3>
        <input
          type="text"
          v-model="newScriptName"
          placeholder="脚本名称 (将作为目录名)"
          ref="newScriptNameInput"
          @keyup.enter="addScript"
          @keyup.esc="closeAddModal"
        />
        <div class="modal-actions">
          <button @click="closeAddModal">取消</button>
          <button @click="addScript">确认创建</button>
        </div>
      </div>
    </div>

    <!-- Add Config Field Modal -->
    <div v-if="showAddConfigModal" class="modal-overlay" @click.self="closeAddConfigModal">
      <div class="modal-content config-modal">
        <h3>添加配置项</h3>
        <div class="config-form-modal">
          <div class="form-group">
            <label for="configName">配置名称 (英文)</label>
            <input
              type="text"
              id="configName"
              v-model="newConfigField.name"
              placeholder="例如: max_timeout"
              ref="newConfigNameInput"
              @keyup.enter="addConfigField"
              @keyup.esc="closeAddConfigModal"
              :key="configModalKey"
            />
          </div>

          <div class="form-group">
            <label for="configLabel">显示标签</label>
            <input
              type="text"
              id="configLabel"
              v-model="newConfigField.label"
              placeholder="例如: 最大超时时间"
            />
          </div>

          <div class="form-group">
            <label for="configType">配置类型</label>
            <select id="configType" v-model="newConfigField.type">
              <option value="text">文本</option>
              <option value="number">数字</option>
              <option value="checkbox">复选框</option>
              <option value="select">下拉选择</option>
            </select>
          </div>

          <div v-if="newConfigField.type === 'select'" class="form-group">
            <label for="configOptions">选项 (每行一个)</label>
            <textarea
              id="configOptions"
              v-model="newConfigField.optionsText"
              placeholder="选项1&#10;选项2&#10;选项3"
              rows="4"
            ></textarea>
          </div>

          <div class="form-group">
            <label for="configDefault">默认值</label>
            <input
              v-if="newConfigField.type === 'text'"
              type="text"
              id="configDefault"
              v-model="newConfigField.default"
              placeholder="默认文本值"
            />
            <input
              v-else-if="newConfigField.type === 'number'"
              type="number"
              id="configDefault"
              v-model="newConfigField.default"
              placeholder="默认数字值"
            />
            <input
              v-else-if="newConfigField.type === 'checkbox'"
              type="checkbox"
              id="configDefault"
              v-model="newConfigField.default"
            />
            <select
              v-else-if="newConfigField.type === 'select'"
              id="configDefault"
              v-model="newConfigField.default"
            >
              <option value="">请选择默认值</option>
              <option v-for="option in newConfigField.optionsText.split('\n').filter(o => o.trim())" :key="option" :value="option.trim()">
                {{ option.trim() }}
              </option>
            </select>
          </div>
        </div>

        <div class="modal-actions">
          <button @click="closeAddConfigModal">取消</button>
          <button @click="addConfigField" :disabled="!newConfigField.name || !newConfigField.label">确认添加</button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteConfirmModal" class="modal-overlay" @click.self="cancelDeleteConfigField">
      <div class="modal-content">
        <h3>确认删除</h3>
        <p v-if="configItemToDelete">
          您确定要删除配置项 "<strong>{{ configItemToDelete.field.label }}</strong>" 吗？<br />
          此操作不可恢复。
        </p>
        <div class="modal-actions">
          <button @click="cancelDeleteConfigField">取消</button>
          <button @click="confirmDeleteConfigField" class="danger-btn">确认删除</button>
        </div>
      </div>
    </div>

    <!-- 自定义弹窗 -->
    <CustomDialog
      :visible="dialog.visible"
      :type="dialog.type"
      :title="dialog.title"
      :message="dialog.message"
      :confirmText="dialog.confirmText"
      :isDangerous="dialog.isDangerous"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
      @close="handleDialogClose"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, inject } from 'vue'
import CustomDialog from './CustomDialog.vue'
import { pageStateManager } from '../utils/pageStateManager.js'

const scripts = ref([])
const selectedScript = ref(null)
const config = ref({ schema: [], values: {} })
const originalValuesStr = ref('{}')
const isDirty = ref(false)
const logOutput = ref('💡 欢迎使用JS脚本管理器\n📝 请选择一个脚本开始使用。')
const isRunning = ref(false)
const logContentRef = ref(null)

// 自启动配置
const autoStartEnabled = ref(false)
const autoStartDelay = ref(0)
const autoStartConfig = ref(null)
const isTogglingAutoStart = ref(false)

const showAddModal = ref(false)
const newScriptName = ref('')
const newScriptNameInput = ref(null)

// 配置项管理相关
const showAddConfigModal = ref(false)
const newConfigNameInput = ref(null)
const addConfigBtn = ref(null)
const configModalKey = ref(0) // 用于强制重新创建输入框
const newConfigField = ref({
  name: '',
  label: '',
  type: 'text',
  default: '',
  optionsText: ''
})

// Delete confirmation modal state
const showDeleteConfirmModal = ref(false)
const configItemToDelete = ref(null) // Will store { index, field }

// 自定义弹窗状态
const dialog = ref({
  visible: false,
  type: 'info',
  title: '提示',
  message: '',
  confirmText: '确定',
  isDangerous: false,
  onConfirm: null,
  onCancel: null
})

// 页面状态管理
const pageId = 'JsScripts'
// 移除inject依赖，直接使用pageStateManager

// 状态持久化
function savePageState() {
  const state = {
    selectedScriptName: selectedScript.value?.name || null,
    config: config.value,
    isDirty: isDirty.value,
    autoStartEnabled: autoStartEnabled.value,
    autoStartDelay: autoStartDelay.value,
    logOutput: logOutput.value,
    scrollPosition: document.querySelector('.script-list-panel ul')?.scrollTop || 0
  }
  
  pageStateManager.savePageState(pageId, state)
  console.log(`[${pageId}] 状态已保存:`, state)
}

function restorePageState() {
  const state = pageStateManager.getPageState(pageId)
  if (!state) {
    console.log(`[${pageId}] 没有找到保存的状态`)
    return
  }
  
  console.log(`[${pageId}] 恢复状态:`, state)
  
  // 恢复选中的脚本
  if (state.selectedScriptName && scripts.value.length > 0) {
    const script = scripts.value.find(s => s.name === state.selectedScriptName)
    if (script) {
      selectedScript.value = script
      config.value = state.config || { schema: [], values: {} }
      isDirty.value = state.isDirty || false
      autoStartEnabled.value = state.autoStartEnabled || false
      autoStartDelay.value = state.autoStartDelay || 0
      logOutput.value = state.logOutput || '💡 欢迎使用JS脚本管理器\n📝 请选择一个脚本开始使用。'
    }
  }
  
  // 恢复滚动位置
  if (state.scrollPosition) {
    nextTick(() => {
      const listElement = document.querySelector('.script-list-panel ul')
      if (listElement) {
        listElement.scrollTop = state.scrollPosition
      }
    })
  }
}

// 监听状态变化并自动保存
watch([selectedScript, config, isDirty, autoStartEnabled, autoStartDelay, logOutput], () => {
  savePageState()
}, { deep: true })

// 自定义弹窗函数
function showDialog(options) {
  dialog.value = {
    visible: true,
    type: options.type || 'info',
    title: options.title || '提示',
    message: options.message || '',
    confirmText: options.confirmText || '确定',
    isDangerous: options.isDangerous || false,
    onConfirm: options.onConfirm || null,
    onCancel: options.onCancel || null
  }
}

function showAlert(message, type = 'info', title = '提示') {
  return new Promise((resolve) => {
    showDialog({
      type,
      title,
      message,
      onConfirm: resolve,
      onCancel: resolve
    })
  })
}

function showConfirm(message, title = '确认', isDangerous = false) {
  return new Promise((resolve) => {
    showDialog({
      type: 'confirm',
      title,
      message,
      isDangerous,
      confirmText: isDangerous ? '删除' : '确定',
      onConfirm: () => resolve(true),
      onCancel: () => resolve(false)
    })
  })
}

function handleDialogConfirm() {
  if (dialog.value.onConfirm) {
    dialog.value.onConfirm()
  }
  dialog.value.visible = false
}

function handleDialogCancel() {
  if (dialog.value.onCancel) {
    dialog.value.onCancel()
  }
  dialog.value.visible = false
}

function handleDialogClose() {
  dialog.value.visible = false
}

// 辅助函数：格式化日志输出，处理换行符和时间戳
function formatLogOutput(message, addTimestamp = true) {
  const timestamp = addTimestamp ? `[${new Date().toLocaleTimeString()}] ` : ''
  return timestamp + message.replace(/\\n/g, '\n')
}

// 辅助函数：添加日志
function addLog(message, addTimestamp = true) {
  logOutput.value += formatLogOutput(message, addTimestamp)
  // Auto-scroll to bottom
  nextTick(() => {
    if (logContentRef.value) {
      logContentRef.value.scrollTop = logContentRef.value.scrollHeight
    }
  })
}

// 辅助函数：设置日志（清空并设置新内容）
function setLog(message, addTimestamp = true) {
  logOutput.value = formatLogOutput(message, addTimestamp)
}

// 监听模态框显示状态，确保焦点设置
watch(showAddModal, async (newValue) => {
  if (newValue) {
    // 模态框显示时，确保焦点设置
    await nextTick()
    setTimeout(() => {
      newScriptNameInput.value?.focus()
    }, 150) // 稍微增加延迟确保模态框动画完成
  }
})

// 监听配置项模态框显示状态，确保焦点设置
watch(showAddConfigModal, async (newValue) => {
  if (newValue) {
    await nextTick()
    setTimeout(() => {
      newConfigNameInput.value?.focus()
    }, 150)
  }
})

async function fetchScripts() {
  scripts.value = await window.api.getJsScripts()
  if (selectedScript.value) {
    const stillExists = scripts.value.some((s) => s.name === selectedScript.value.name)
    if (!stillExists) {
      selectedScript.value = null
      config.value = { schema: [], values: {} }
      logOutput.value = '📝 请选择一个脚本。'
    }
  }
}

onMounted(async () => {
  await fetchScripts()
  
  // 恢复页面状态
  restorePageState()
  
  // 如果没有恢复到选中的脚本，默认选择第一个
  if (!selectedScript.value && scripts.value.length > 0) {
    selectScript(scripts.value[0])
  }
  
  console.log(`[${pageId}] 组件已挂载并恢复状态`)
})

watch(
  () => config.value.values,
  (newValues) => {
    if (newValues && selectedScript.value) {
      isDirty.value = JSON.stringify(newValues) !== originalValuesStr.value
    }
  },
  { deep: true }
)

async function selectScript(script) {
  if (isDirty.value) {
    const confirmed = await showConfirm('当前配置有未保存的更改，确定要切换吗？更改将丢失。', '确认切换', true)
    if (confirmed) {
      isDirty.value = false
    } else {
      return
    }
  }
  selectedScript.value = script
  logOutput.value = `✅ 已选择脚本: ${script.name}\n🚀 准备就绪，可以运行脚本。`
  const newConfig = await window.api.getJsScriptConfig(script.name)
  if (newConfig) {
    config.value = newConfig
    originalValuesStr.value = JSON.stringify(newConfig.values || {})
  } else {
    config.value = { schema: [], values: {} }
    originalValuesStr.value = '{}'
  }
  
  // 加载自启动配置
  await loadAutoStartConfig(script.name)
  isDirty.value = false
}

// 加载自启动配置
async function loadAutoStartConfig(scriptName) {
  try {
    const autoStartScripts = await window.api.autoStart.getScripts()
    const scriptConfig = autoStartScripts.find(s => 
      s.script_type === 'js' && s.script_name === scriptName
    )
    
    if (scriptConfig) {
      autoStartConfig.value = scriptConfig
      autoStartEnabled.value = Boolean(scriptConfig.enabled)
      autoStartDelay.value = scriptConfig.delay_seconds || 0
    } else {
      autoStartConfig.value = null
      autoStartEnabled.value = false
      autoStartDelay.value = 0
    }
  } catch (error) {
    console.error('加载自启动配置失败:', error)
    autoStartConfig.value = null
    autoStartEnabled.value = false
    autoStartDelay.value = 0
  }
}

// 切换自启动状态
async function toggleAutoStart() {
  if (!selectedScript.value) return
  
  isTogglingAutoStart.value = true
  try {
    if (autoStartEnabled.value) {
      // 取消自启动
      const autoStartScripts = await window.api.autoStart.getScripts()
      const scriptConfig = autoStartScripts.find(s => 
        s.script_type === 'js' && s.script_name === selectedScript.value.name
      )
      
      if (scriptConfig) {
        await window.api.autoStart.deleteScript(scriptConfig.id)
        autoStartEnabled.value = false
        autoStartDelay.value = 0
        addLog(`✅ 已取消 ${selectedScript.value.name} 的自启动配置`)
      }
    } else {
      // 设为自启动
      const result = await window.api.autoStart.addScript('js', selectedScript.value.name, autoStartDelay.value)
      autoStartEnabled.value = true
      addLog(`✅ 已将 ${selectedScript.value.name} 设为自启动脚本 (延迟: ${autoStartDelay.value}秒)`)
    }
  } catch (error) {
    console.error('设置自启动失败:', error)
    addLog(`❌ 设置自启动失败: ${error.message}`)
  } finally {
    isTogglingAutoStart.value = false
  }
}

// 更新自启动延迟
async function updateAutoStartDelay() {
  if (!selectedScript.value || !autoStartEnabled.value) return
  
  try {
    const autoStartScripts = await window.api.invoke('auto-start:get-scripts')
    const scriptConfig = autoStartScripts.find(s => 
      s.script_type === 'js' && s.script_name === selectedScript.value.name
    )
    
    if (scriptConfig) {
      await window.api.invoke('auto-start:update-script', 
        scriptConfig.id, 
        true, 
        autoStartDelay.value
      )
      addLog(`✅ 已更新 ${selectedScript.value.name} 的启动延迟为 ${autoStartDelay.value} 秒`)
    }
  } catch (error) {
    console.error('更新自启动延迟失败:', error)
    addLog(`❌ 更新启动延迟失败: ${error.message}`)
  }
}

async function openAddModal() {
  showAddModal.value = true
  newScriptName.value = ''
  await nextTick()
  setTimeout(() => {
    newScriptNameInput.value?.focus()
  }, 100)
}

function closeAddModal() {
  showAddModal.value = false
  newScriptName.value = ''
}

async function addScript() {
  const name = newScriptName.value.trim()
  if (!name) {
    await showAlert('脚本名称不能为空', 'warning')
    return
  }
  if (/[\\\\/:*?"<>|]/.test(name)) {
    await showAlert('脚本名称包含非法字符。', 'error')
    return
  }

  try {
    const newScript = await window.api.addJsScript(name)
    await fetchScripts()
    const scriptInList = scripts.value.find((s) => s.name === newScript.name)
    if (scriptInList) {
      selectScript(scriptInList)
    }
    closeAddModal()
  } catch (error) {
    console.error('添加脚本时出错:', error)
    await showAlert(`添加脚本时出错: ${error.message}`, 'error')
  }
}

async function deleteScript(script) {
  const confirmed = await showConfirm(`确定要删除脚本目录 “${script.name}” 吗？此操作不可恢复。`, '删除脚本', true)
  if (confirmed) {
    try {
      await window.api.deleteJsScript(script.name)
      if (selectedScript.value && selectedScript.value.name === script.name) {
        selectedScript.value = null
        config.value = { schema: [], values: {} }
      }
      await fetchScripts()
      if (!selectedScript.value && scripts.value.length > 0) {
        selectScript(scripts.value[0])
      }
    } catch (error) {
      console.error('删除脚本时出错:', error)
      await showAlert(`删除脚本时出错: ${error.message}`, 'error')
    }
  }
}

async function saveConfig() {
  if (!selectedScript.value || !isDirty.value) return
  try {
    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      values: JSON.parse(JSON.stringify(config.value.values || {})),
      schema: JSON.parse(JSON.stringify(config.value.schema || []))
    }
    await window.api.updateJsScriptConfig(cleanParams)
    originalValuesStr.value = JSON.stringify(config.value.values)
    isDirty.value = false
    return true
  } catch (error) {
    console.error('保存配置时出错:', error)
    await showAlert(`保存配置时出错: ${error.message}`, 'error')
    return false
  }
}

async function runScript() {
  if (!selectedScript.value || !config.value.values) return

  addLog(`\n\n- - - - - - - - [ ${new Date().toLocaleString()} ] - - - - - - - -\n`, false)

  if (isDirty.value) {
    addLog(`💾 正在自动保存配置...\n`, false)
    const saveSuccess = await saveConfig()
    if (!saveSuccess) {
      addLog(`❌ 配置保存失败，运行已中止。\n`, false)
      return
    }
    addLog(`✅ 配置已保存。\n`, false)
  }

  try {
    isRunning.value = true
    addLog(`🚀 正在运行脚本: ${selectedScript.value.name}...\n`, false)

    const cleanParams = {
      scriptName: String(selectedScript.value.name),
      configValues: JSON.parse(JSON.stringify(config.value.values || {}))
    }

    const result = await window.api.runJsScript(cleanParams)

    addLog(`\n=== 执行结果 ===\n`, false)
    if (result.stdout && result.stdout.trim()) {
      addLog(`📄 标准输出:\n${result.stdout}\n`, false)
    }
    if (result.stderr && result.stderr.trim()) {
      addLog(`⚠️ 错误输出:\n${result.stderr}\n`, false)
    }
    if (!result.stdout?.trim() && !result.stderr?.trim()) {
      addLog(`✅ 脚本执行完成，无输出内容。\n`, false)
    }
  } catch (error) {
    console.error('运行JS脚本时出错:', error)
    addLog(`\n❌ 脚本执行失败\n`, false)
    if (error.name) addLog(`🏷️ 错误类型: ${error.name}\n`, false)
    if (error.message) addLog(`💬 错误信息: ${error.message}\n`, false)
    if (error.exitCode !== undefined) addLog(`🔢 退出码: ${error.exitCode}\n`, false)
    if (error.debug) {
      addLog(`\n🔍 调试信息:\n${JSON.stringify(error.debug, null, 2)}\n`, false)
    }
  } finally {
    isRunning.value = false
  }
}

function clearLog() {
  setLog('💡 日志已清空。\n')
}

async function testNodeEnvironment() {
  try {
    isRunning.value = true
    setLog('🔍 正在测试Node.js环境...\n')
    const result = await window.api.testNodeEnvironment()
    if (result.success) {
      addLog(`✅ Node.js环境正常\n📦 版本: ${result.version}\n`, false)
    } else {
      addLog(`❌ Node.js环境异常\n⚠️ 错误: ${result.error}\n`, false)
    }
  } catch (error) {
    console.error('测试Node.js环境时出错:', error)
    addLog(`❌ 测试失败\n💬 错误: ${error.message}\n`, false)
  } finally {
    isRunning.value = false
  }
}

async function openAddConfigModal() {
  if (!selectedScript.value) {
    await showAlert('请先选择一个脚本', 'warning')
    return
  }
  configModalKey.value++
  newConfigField.value = {
    name: '',
    label: '',
    type: 'text',
    default: '',
    optionsText: ''
  }
  showAddConfigModal.value = true
  await nextTick()
  newConfigNameInput.value?.focus()
}

function closeAddConfigModal() {
  showAddConfigModal.value = false
  newConfigField.value = { name: '', label: '', type: 'text', default: '', optionsText: '' }
}

async function addConfigField() {
  const field = newConfigField.value
  if (!field.name.trim() || !field.label.trim()) {
    await showAlert('请填写配置名称和显示标签', 'warning')
    return
  }
  if (config.value.schema.some(f => f.name === field.name.trim())) {
    await showAlert('配置名称已存在，请使用其他名称', 'warning')
    return
  }
  const newField = {
    name: field.name.trim(),
    label: field.label.trim(),
    type: field.type
  }
  if (field.type === 'select') {
    const options = field.optionsText.split('\n').map(o => o.trim()).filter(o => o.length > 0)
    if (options.length === 0) {
      await showAlert('下拉选择类型需要至少一个选项', 'warning')
      return
    }
    newField.options = options
    newField.default = field.default || options[0]
  } else if (field.type === 'number') {
    newField.default = Number(field.default) || 0
  } else if (field.type === 'checkbox') {
    newField.default = Boolean(field.default)
  } else {
    newField.default = field.default || ''
  }
  config.value.schema.push(newField)
  config.value.values[newField.name] = newField.default
  isDirty.value = true
  closeAddConfigModal()
}

function promptDeleteConfigField(index) {
  const field = config.value.schema[index]
  if (!field) return
  configItemToDelete.value = { index, field }
  showDeleteConfirmModal.value = true
}

function cancelDeleteConfigField() {
  showDeleteConfirmModal.value = false
  configItemToDelete.value = null
}

async function confirmDeleteConfigField() {
  if (!configItemToDelete.value) return
  const { index, field } = configItemToDelete.value
  config.value.schema.splice(index, 1)
  delete config.value.values[field.name]
  isDirty.value = true
  showDeleteConfirmModal.value = false
  configItemToDelete.value = null
  await nextTick()
  addConfigBtn.value?.focus()
}
</script>

<style scoped>
.js-scripts-container {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: var(--background);
  color: var(--text-primary);
  overflow: hidden;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}
.script-list-panel {
  width: 250px;
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  background-color: var(--background-secondary);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}
.panel-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--background-tertiary);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}
.panel-header h3 {
  margin: 0;
  color: var(--text-primary);
  transition: color var(--transition-normal);
}
.add-btn {
  background-color: var(--success);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all var(--transition-fast);
}
.add-btn:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}
.script-list-panel ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex-grow: 1;
}
.script-list-panel li {
  padding: 0.8rem 1rem;
  margin-bottom: 0.3rem;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}
.script-list-panel li:hover {
  background-color: var(--card-hover);
  color: var(--text-primary);
}
.script-list-panel li.active {
  background-color: var(--primary);
  color: white;
  font-weight: 500;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}
.delete-btn {
  background: none;
  border: none;
  color: #ff5555;
  cursor: pointer;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s;
}
.script-list-panel li:hover .delete-btn,
.script-list-panel li.active .delete-btn {
  visibility: visible;
  opacity: 1;
}
.right-panel {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.script-config-panel {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}
.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}
.title-input {
  font-size: 1.5rem;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-weight: bold;
}
.button-group button {
  margin-left: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
.run-btn {
  background-color: var(--primary);
  color: white;
}
.run-btn:disabled {
  background-color: #555;
  cursor: not-allowed;
}
.save-btn {
  background-color: #555;
  color: #ccc;
}
.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.save-btn:not(:disabled) {
  background-color: var(--success);
  color: white;
}
.test-btn {
  padding: 8px 16px;
  background-color: var(--warning);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}
.test-btn:hover:not(:disabled) {
  background-color: #6b46c1;
}
.test-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}
.config-form {
  overflow-y: auto;
}
.form-group {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}
.form-group label {
  margin-bottom: 0.5rem;
  color: #ccc;
}
.form-group input,
.form-group select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--input-border);
  background-color: var(--input-background);
  color: var(--text-primary);
}
.form-checkbox {
  width: 20px;
  height: 20px;
  align-self: flex-start;
}
.no-config {
  text-align: center;
  padding: 2rem;
  color: #888;
}
.log-output-panel {
  flex-grow: 1;
  background-color: #0d1117;
  margin: 0 1rem 1rem 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #30363d;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
.log-output-panel h4::before {
  content: '📋';
  margin-right: 8px;
}
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}
.clear-log-btn {
  padding: 4px 10px;
  background-color: var(--button-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all var(--transition-fast);
}

.clear-log-btn:hover {
  background-color: var(--button-secondary-hover);
  color: var(--text-primary);
}
.clear-log-btn:hover:not(:disabled) {
  background-color: #777;
}
.clear-log-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
  opacity: 0.7;
}
.log-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  flex-grow: 1;
  overflow-y: auto;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #e6edf3;
  background-color: #010409;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #21262d;
  margin: 0;
  min-height: 150px;
  max-height: 400px;
}
.log-content::-webkit-scrollbar {
  width: 8px;
}
.log-content::-webkit-scrollbar-track {
  background: #21262d;
  border-radius: 4px;
}
.log-content::-webkit-scrollbar-thumb {
  background: #484f58;
  border-radius: 4px;
}
.log-content::-webkit-scrollbar-thumb:hover {
  background: #6e7681;
  font-size: 0.9rem;
  color: #ddd;
}
.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #888;
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-content {
  background-color: #2c2c2c;
  padding: 2rem;
  border-radius: 8px;
  width: 400px;
}
.modal-content h3 {
  margin-top: 0;
}
.modal-content input {
  width: 100%;
  padding: 0.5rem;
  margin-top: 1rem;
  border-radius: 4px;
  border: 1px solid var(--input-border);
  background-color: var(--input-background);
  color: var(--text-primary);
}
.modal-actions {
  margin-top: 1.5rem;
  text-align: right;
}
.modal-actions button {
  margin-left: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
.modal-actions button:first-child {
  background-color: var(--button-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border);
}
.modal-actions button:first-child:hover {
  background-color: var(--button-secondary-hover);
  color: var(--text-primary);
}
.modal-actions button:last-child {
  background-color: var(--primary);
  color: white;
}
.modal-actions .danger-btn {
  background-color: var(--error);
  color: white;
}
.modal-actions .danger-btn:hover {
  background-color: #d9363e;
}
.config-modal {
  width: 500px;
  max-width: 90vw;
}
.config-form-modal .form-group {
  margin-bottom: 1rem;
}
.config-form-modal label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
}
.config-form-modal input,
.config-form-modal select,
.config-form-modal textarea {
  width: 100%;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid var(--input-border);
  background-color: var(--input-background);
  color: var(--text-primary);
  font-size: 14px;
  box-sizing: border-box;
}
.config-form-modal input:focus,
.config-form-modal select:focus,
.config-form-modal textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.config-form-modal textarea {
  resize: vertical;
  min-height: 80px;
}
.config-section {
  margin-bottom: 1rem;
}
.config-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 16px;
}
.config-actions {
  display: flex;
  gap: 8px;
}

.auto-start-config {
  margin-top: 2rem;
  padding: 1rem;
  background: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--border);
}
.auto-start-config .config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
.auto-start-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--primary);
  background: transparent;
  color: var(--primary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}
.auto-start-btn:hover:not(:disabled) {
  background: var(--primary);
  color: white;
}
.auto-start-btn.active {
  background: var(--success);
  border-color: var(--success);
  color: white;
}
.auto-start-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.auto-start-options {
  padding: 0.5rem 0;
}
.auto-start-options .form-group {
  margin-bottom: 0.5rem;
}
.auto-start-options label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: var(--text-primary);
}
.auto-start-options input[type="number"] {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  background-color: var(--input-background);
  color: var(--text-primary);
}
.add-config-btn {
  padding: 6px 12px;
  background-color: var(--success);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}
.add-config-btn:hover:not(:disabled) {
  background-color: #389e0d;
}
.add-config-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}
.form-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
.remove-field-btn {
  padding: 4px 8px;
  background-color: var(--error);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}
.remove-field-btn:hover:not(:disabled) {
  background-color: #d9363e;
}
.remove-field-btn:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}
</style>