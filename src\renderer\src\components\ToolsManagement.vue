<template>
  <div class="tools-management">
    <!-- 工具列表面板 -->
    <div class="tool-list-panel">
      <div class="panel-header">
        <h3>工具列表</h3>
        <div class="tool-count">{{ tools.length }} 个工具</div>
      </div>
      
      <div class="search-section">
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="搜索工具..." 
          class="search-input"
        />
      </div>

      <div class="category-filter">
        <button 
          v-for="category in categories" 
          :key="category.id"
          :class="['category-btn', { active: selectedCategory === category.id }]"
          @click="selectedCategory = category.id"
        >
          <span class="category-icon">{{ category.icon }}</span>
          {{ category.name }}
        </button>
      </div>

      <ul class="tool-list">
        <li 
          v-for="tool in filteredTools" 
          :key="tool.id"
          :class="['tool-item', { 
            active: selectedTool?.id === tool.id,
            disabled: !tool.available 
          }]"
          @click="selectTool(tool)"
        >
          <div class="tool-info">
            <span class="tool-icon">{{ tool.icon }}</span>
            <div class="tool-details">
              <div class="tool-name">{{ tool.name }}</div>
              <div class="tool-category">{{ tool.categoryName }}</div>
            </div>
          </div>
          <div class="tool-status">
            <span v-if="tool.available" class="status-available">可用</span>
            <span v-else class="status-unavailable">敬请期待</span>
          </div>
        </li>
      </ul>
    </div>

    <!-- 工具详情面板 -->
    <div class="tool-detail-panel">
      <div v-if="!selectedTool" class="no-selection">
        <div class="no-selection-icon">🔧</div>
        <h3>选择一个工具</h3>
        <p>从左侧列表中选择一个工具来查看详细信息和进行操作</p>
      </div>

      <div v-else class="tool-details-content">
        <!-- 工具头部信息 -->
        <div class="tool-header">
          <div class="tool-title-section">
            <span class="tool-large-icon">{{ selectedTool.icon }}</span>
            <div class="tool-title-info">
              <h2>{{ selectedTool.name }}</h2>
              <p class="tool-description">{{ selectedTool.description }}</p>
            </div>
          </div>
          <div class="tool-actions">
            <button 
              v-if="selectedTool.available"
              :class="['primary-action-btn', selectedTool.id]"
              @click="executeTool(selectedTool)"
            >
              <span class="btn-icon">{{ selectedTool.actionIcon }}</span>
              {{ selectedTool.actionText }}
            </button>
            <button v-else class="disabled-action-btn" disabled>
              <span class="btn-icon">🔒</span>
              敬请期待
            </button>
          </div>
        </div>

        <!-- 工具功能特性 -->
        <div class="tool-features-section">
          <h4>功能特性</h4>
          <div class="features-grid">
            <div 
              v-for="feature in selectedTool.features" 
              :key="feature"
              class="feature-item"
            >
              <span class="feature-icon">✨</span>
              {{ feature }}
            </div>
          </div>
        </div>

        <!-- 快捷键信息 -->
        <div v-if="selectedTool.shortcuts" class="shortcuts-section">
          <h4>快捷键</h4>
          <div class="shortcuts-grid">
            <div 
              v-for="shortcut in selectedTool.shortcuts" 
              :key="shortcut.label"
              class="shortcut-item"
            >
              <span class="shortcut-label">{{ shortcut.label }}:</span>
              <span class="shortcut-key">{{ shortcut.key }}</span>
            </div>
          </div>
        </div>

        <!-- 工具使用说明 -->
        <div class="usage-section">
          <h4>使用说明</h4>
          <div class="usage-content">
            <p>{{ selectedTool.usage }}</p>
          </div>
        </div>

        <!-- 工具组件嵌入区域 -->
        <div v-if="selectedTool.available && selectedTool.embedComponent" class="tool-embed-section">
          <h4>工具界面</h4>
          <div class="tool-embed-container">
            <component 
              :is="getToolComponent(selectedTool.id)" 
              v-if="showEmbeddedTool"
              @navigate-to="handleNavigateTo"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, inject, markRaw } from 'vue'
import { pageStateManager } from '../utils/pageStateManager.js'

// 导入工具组件
import EasyVoiceWebTTS from './EasyVoiceWebTTS.vue'
import VoiceCloning from './VoiceCloning.vue'
import SnakeGame from './SnakeGame.vue'

const emit = defineEmits(['navigate-to'])

// 页面状态管理
const pageId = 'ToolsManagement'

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')
const selectedTool = ref(null)
const showEmbeddedTool = ref(false)

// 工具分类
const categories = ref([
  { id: 'all', name: '全部', icon: '📁' },
  { id: 'image', name: '图像工具', icon: '🖼️' },
  { id: 'audio', name: '音频工具', icon: '🔊' },
  { id: 'system', name: '系统工具', icon: '🖥️' },
  { id: 'entertainment', name: '娱乐工具', icon: '🎮' }
])

// 工具列表
const tools = ref([
  {
    id: 'screenshot',
    name: '截图工具',
    icon: '📸',
    category: 'image',
    categoryName: '图像工具',
    description: '强大的截图工具，支持全屏、区域、窗口捕捉和延时截图',
    features: ['全屏截图', '区域选择', '窗口捕捉', '延时截图', '快捷键支持'],
    shortcuts: [
      { label: '全屏', key: 'F1' },
      { label: '窗口', key: 'Alt+W' },
      { label: '延时', key: 'Ctrl+D' }
    ],
    usage: '点击对应按钮或使用快捷键进行截图操作。支持多种截图模式，满足不同使用场景。',
    available: true,
    actionIcon: '📸',
    actionText: '开始截图',
    embedComponent: false
  },
  {
    id: 'screen-capture',
    name: '屏幕捕获',
    icon: '📷',
    category: 'image',
    categoryName: '图像工具',
    description: '专业的屏幕捕获工具，支持精确选择、实时编辑和多种输出格式',
    features: ['精确选择', '实时编辑', '多种格式', '快速分享'],
    usage: '启动后可以精确选择屏幕区域进行捕获，支持实时编辑和多种输出格式。',
    available: true,
    actionIcon: '📷',
    actionText: '开始捕获',
    embedComponent: false
  },
  {
    id: 'text-to-speech',
    name: '文本转语音',
    icon: '🗣️',
    category: 'audio',
    categoryName: '音频工具',
    description: '使用本地TTS服务将文本转换为自然语音，支持多种语言和语音风格',
    features: ['本地服务', '多语言支持', '语速调节', '音频导出', '实时预览'],
    usage: '输入文本内容，选择语音参数，即可生成高质量的语音文件。支持实时预览和批量转换。',
    available: true,
    actionIcon: '🗣️',
    actionText: '开始转换',
    embedComponent: true,
    component: 'EasyVoiceWebTTS'
  },
  {
    id: 'voice-cloning',
    name: '语音克隆',
    icon: '🎭',
    category: 'audio',
    categoryName: '音频工具',
    description: '使用AI技术克隆和生成自定义语音，支持声音模拟和语音合成',
    features: ['AI克隆', '声音模拟', '语音合成', '自定义音色', '高质量输出'],
    usage: '上传音频样本，训练AI模型，生成具有相同音色特征的语音内容。',
    available: true,
    actionIcon: '🎭',
    actionText: '开始克隆',
    embedComponent: true,
    component: 'VoiceCloning'
  },
  {
    id: 'snake-game',
    name: '贪吃蛇游戏',
    icon: '🐍',
    category: 'entertainment',
    categoryName: '娱乐工具',
    description: '经典的贪吃蛇游戏，支持手动和自动模式，可调节游戏速度',
    features: ['自动模式', '速度调节', '高分记录', '智能AI', '多种主题'],
    usage: '使用方向键控制蛇的移动，吃到食物获得分数。支持AI自动游戏模式。',
    available: true,
    actionIcon: '🎮',
    actionText: '开始游戏',
    embedComponent: true,
    component: 'SnakeGame'
  },
  {
    id: 'file-compress',
    name: '压缩工具',
    icon: '🗜️',
    category: 'system',
    categoryName: '系统工具',
    description: '文件压缩和解压缩工具',
    features: ['多格式支持', '批量处理', '压缩率优化'],
    usage: '即将推出...',
    available: false,
    actionIcon: '🔒',
    actionText: '敬请期待',
    embedComponent: false
  },
  {
    id: 'system-monitor',
    name: '系统监控',
    icon: '🖥️',
    category: 'system',
    categoryName: '系统工具',
    description: '系统性能监控工具',
    features: ['实时监控', '性能分析', '资源统计'],
    usage: '即将推出...',
    available: false,
    actionIcon: '🔒',
    actionText: '敬请期待',
    embedComponent: false
  },
  {
    id: 'system-clean',
    name: '系统清理',
    icon: '🧹',
    category: 'system',
    categoryName: '系统工具',
    description: '系统垃圾清理工具',
    features: ['垃圾清理', '注册表优化', '启动项管理'],
    usage: '即将推出...',
    available: false,
    actionIcon: '🔒',
    actionText: '敬请期待',
    embedComponent: false
  }
])

// 组件映射
const componentMap = {
  EasyVoiceWebTTS: markRaw(EasyVoiceWebTTS),
  VoiceCloning: markRaw(VoiceCloning),
  SnakeGame: markRaw(SnakeGame)
}

// 计算属性
const filteredTools = computed(() => {
  let filtered = tools.value

  // 按分类过滤
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(tool => tool.category === selectedCategory.value)
  }

  // 按搜索关键词过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tool => 
      tool.name.toLowerCase().includes(query) ||
      tool.description.toLowerCase().includes(query) ||
      tool.features.some(feature => feature.toLowerCase().includes(query))
    )
  }

  return filtered
})

// 方法
function selectTool(tool) {
  if (!tool.available) return
  
  selectedTool.value = tool
  showEmbeddedTool.value = false
  
  // 保存状态
  savePageState()
  
  console.log(`[${pageId}] 选择工具: ${tool.name}`)
}

function executeTool(tool) {
  if (!tool.available) return

  if (tool.embedComponent && tool.component) {
    // 嵌入式工具 - 在当前页面显示
    showEmbeddedTool.value = true
    console.log(`[${pageId}] 启动嵌入式工具: ${tool.name}`)
  } else {
    // 外部工具 - 执行特定操作
    switch (tool.id) {
      case 'screenshot':
        startScreenshot()
        break
      case 'screen-capture':
        startScreenCapture()
        break
      default:
        console.log(`[${pageId}] 执行工具: ${tool.name}`)
    }
  }
}

function getToolComponent(toolId) {
  const tool = tools.value.find(t => t.id === toolId)
  return tool && tool.component ? componentMap[tool.component] : null
}

function handleNavigateTo(componentName) {
  emit('navigate-to', componentName)
}

// 工具操作方法
async function startScreenshot() {
  try {
    console.log('启动截图工具...')
    // 这里可以调用截图相关的API
  } catch (error) {
    console.error('启动截图工具失败:', error)
  }
}

async function startScreenCapture() {
  try {
    await window.api.createCaptureWindow()
    console.log('启动屏幕捕获工具...')
  } catch (error) {
    console.error('启动屏幕捕获失败:', error)
  }
}

// 状态持久化
function savePageState() {
  const state = {
    selectedToolId: selectedTool.value?.id || null,
    searchQuery: searchQuery.value,
    selectedCategory: selectedCategory.value,
    showEmbeddedTool: showEmbeddedTool.value,
    scrollPosition: document.querySelector('.tool-list')?.scrollTop || 0
  }
  
  pageStateManager.savePageState(pageId, state)
  console.log(`[${pageId}] 状态已保存:`, state)
}

function restorePageState() {
  const state = pageStateManager.getPageState(pageId)
  if (!state) {
    console.log(`[${pageId}] 没有找到保存的状态`)
    return
  }
  
  console.log(`[${pageId}] 恢复状态:`, state)
  
  // 恢复选中的工具
  if (state.selectedToolId) {
    const tool = tools.value.find(t => t.id === state.selectedToolId)
    if (tool) {
      selectedTool.value = tool
      showEmbeddedTool.value = state.showEmbeddedTool || false
    }
  }
  
  // 恢复搜索和分类
  searchQuery.value = state.searchQuery || ''
  selectedCategory.value = state.selectedCategory || 'all'
  
  // 恢复滚动位置
  if (state.scrollPosition) {
    nextTick(() => {
      const listElement = document.querySelector('.tool-list')
      if (listElement) {
        listElement.scrollTop = state.scrollPosition
      }
    })
  }
}

// 监听状态变化并自动保存
watch([selectedTool, searchQuery, selectedCategory, showEmbeddedTool], () => {
  savePageState()
}, { deep: true })

// 生命周期
onMounted(() => {
  // 恢复页面状态
  restorePageState()
  
  // 如果没有选中工具，默认选择第一个可用工具
  if (!selectedTool.value) {
    const firstAvailableTool = tools.value.find(tool => tool.available)
    if (firstAvailableTool) {
      selectTool(firstAvailableTool)
    }
  }
  
  console.log(`[${pageId}] 组件已挂载并恢复状态`)
})
</script>

<style scoped>
.tools-management {
  display: flex;
  height: 100vh;
  background: var(--background);
  color: var(--text-primary);
}

/* 工具列表面板 */
.tool-list-panel {
  width: 320px;
  background: var(--sidebar-background);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid var(--border);
  background: var(--sidebar-header-background);
}

.panel-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.tool-count {
  font-size: 14px;
  color: var(--text-secondary);
}

.search-section {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border);
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--input-background);
  color: var(--text-primary);
  font-size: 14px;
  transition: border-color var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.category-filter {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
  text-align: left;
}

.category-btn:hover {
  background: var(--sidebar-item-hover);
  color: var(--text-primary);
}

.category-btn.active {
  background: var(--sidebar-item-active);
  color: var(--primary);
}

.category-icon {
  font-size: 16px;
}

.tool-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  margin: 0;
  list-style: none;
}

.tool-list::-webkit-scrollbar {
  width: 4px;
}

.tool-list::-webkit-scrollbar-track {
  background: transparent;
}

.tool-list::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 2px;
}

.tool-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.tool-item:hover:not(.disabled) {
  background: var(--sidebar-item-hover);
}

.tool-item.active {
  background: var(--sidebar-item-active);
  border-right: 3px solid var(--primary);
}

.tool-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tool-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.tool-icon {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

.tool-details {
  flex: 1;
}

.tool-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 2px 0;
}

.tool-category {
  font-size: 12px;
  color: var(--text-tertiary);
}

.tool-status {
  font-size: 12px;
}

.status-available {
  color: var(--success);
}

.status-unavailable {
  color: var(--text-tertiary);
}

/* 工具详情面板 */
.tool-detail-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
}

.no-selection-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-selection h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: var(--text-primary);
}

.no-selection p {
  margin: 0;
  font-size: 14px;
  max-width: 300px;
  line-height: 1.5;
}

.tool-details-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.tool-details-content::-webkit-scrollbar {
  width: 6px;
}

.tool-details-content::-webkit-scrollbar-track {
  background: transparent;
}

.tool-details-content::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

.tool-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border);
}

.tool-title-section {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.tool-large-icon {
  font-size: 48px;
  margin-top: 4px;
}

.tool-title-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.tool-description {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.tool-actions {
  margin-left: 16px;
}

.primary-action-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.primary-action-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.primary-action-btn.text-to-speech {
  background: #FF9800;
}

.primary-action-btn.text-to-speech:hover {
  background: #F57C00;
}

.primary-action-btn.voice-cloning {
  background: #9C27B0;
}

.primary-action-btn.voice-cloning:hover {
  background: #7B1FA2;
}

.primary-action-btn.snake-game {
  background: #4CAF50;
}

.primary-action-btn.snake-game:hover {
  background: #388E3C;
}

.disabled-action-btn {
  background: var(--surface);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  cursor: not-allowed;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-icon {
  font-size: 16px;
}

.tool-features-section,
.shortcuts-section,
.usage-section,
.tool-embed-section {
  margin-bottom: 32px;
}

.tool-features-section h4,
.shortcuts-section h4,
.usage-section h4,
.tool-embed-section h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: var(--card-background);
  border: 1px solid var(--border);
  border-radius: 8px;
  font-size: 14px;
  color: var(--text-primary);
}

.feature-icon {
  color: var(--primary);
  font-size: 16px;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: var(--card-background);
  border: 1px solid var(--border);
  border-radius: 8px;
  font-size: 14px;
}

.shortcut-label {
  color: var(--text-secondary);
}

.shortcut-key {
  background: var(--surface);
  color: var(--text-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
  font-weight: 500;
  border: 1px solid var(--border);
  font-size: 12px;
}

.usage-content {
  padding: 16px;
  background: var(--card-background);
  border: 1px solid var(--border);
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-secondary);
}

.tool-embed-section {
  border-top: 1px solid var(--border);
  padding-top: 24px;
}

.tool-embed-container {
  background: var(--card-background);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tools-management {
    flex-direction: column;
  }
  
  .tool-list-panel {
    width: 100%;
    height: 300px;
  }
  
  .tool-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .tool-actions {
    margin-left: 0;
    width: 100%;
  }
  
  .primary-action-btn,
  .disabled-action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
