<template>
  <div class="dashboard-container">
    <!-- 顶部导航栏 -->
    <header class="dashboard-header">
      <div class="header-left">
        <h1 class="dashboard-title">工作台</h1>
        <span class="dashboard-subtitle">{{ currentTime }}</span>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="dashboard-main">


      <!-- 数据概览区域 -->
      <section class="data-overview">
        <h2 class="section-title">系统数据</h2>
          <!-- 系统资源 -->
        <div class="stat-category">
          <h3 class="category-title">系统资源</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
                💻
              </div>
              <div class="stat-info">
                <h3>{{ systemStats.cpuUsage }}%</h3>
                <p>CPU使用率</p>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%)">
                🗄️
              </div>
              <div class="stat-info">
                <h3>{{ systemStats.memoryUsage }}%</h3>
                <p>内存使用率</p>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)">
                💾
              </div>
              <div class="stat-info">
                <h3>{{ systemStats.diskUsage }}%</h3>
                <p>磁盘使用率</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 脚本任务 -->
        <div class="stat-category">
          <h3 class="category-title">脚本任务</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)">
                📊
              </div>
              <div class="stat-info">
                <h3>{{ systemStats.totalScripts }}</h3>
                <p>脚本总数</p>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)">
                🎯
              </div>
              <div class="stat-info">
                <h3>{{ systemStats.scheduledTasks }}</h3>
                <p>定时任务</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 运行状态 -->
        <div class="stat-category">
          <h3 class="category-title">运行状态</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)">
                ⏱️
              </div>
              <div class="stat-info">
                <h3>{{ systemStats.uptime }}</h3>
                <p>运行时间</p>
              </div>
            </div>
          </div>
        </div>
      </section>




    </main>



    <!-- 文本转语音 -->
    <div v-if="showTextToSpeech" class="modal-overlay" @click="showTextToSpeech = false">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h3>文本转语音</h3>
          <button class="close-btn" @click="showTextToSpeech = false">×</button>
        </div>
        <div class="modal-body">
          <TextToSpeech />
        </div>
      </div>
    </div>

    <!-- 通知中心 -->
    <div v-if="showNotifications" class="modal-overlay" @click="showNotifications = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>通知中心</h3>
          <button class="close-btn" @click="showNotifications = false">×</button>
        </div>
        <div class="modal-body">
          <div class="notification-item">
            <span class="notification-icon">🎉</span>
            <div class="notification-content">
              <h4>系统更新</h4>
              <p>清风系统已更新至最新版本</p>
              <span class="notification-time">2小时前</span>
            </div>
          </div>
          <div class="notification-item">
            <span class="notification-icon">📊</span>
            <div class="notification-content">
              <h4>数据统计</h4>
              <p>今日已截图 15 次，识别文字 1200 字</p>
              <span class="notification-time">4小时前</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import TextToSpeech from '@renderer/components/TextToSpeech.vue'

// 响应式数据
const currentTime = ref('')
const showTextToSpeech = ref(false)

// 系统状态数据
const systemStats = ref({
  cpuUsage: 0,
  memoryUsage: 0,
  diskUsage: 0,
  uptime: 0,
  totalScripts: 0,
  scheduledTasks: 0
})





// 方法
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}



const openTextToSpeech = () => {
  showTextToSpeech.value = true
}

const openSettings = () => {
  if (window.electron && window.electron.ipcRenderer) {
    window.electron.ipcRenderer.invoke('navigate-to', 'Settings')
  } else {
    console.log('打开设置')
  }
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}



// 添加日志控制和频率限制
let lastStatsCall = 0;
const STATS_CALL_INTERVAL = 3000; // 3秒内只调用一次

// 获取系统状态 - 使用真实数据
const getSystemStats = async () => {
  const now = Date.now();
  
  // 限制调用频率
  if (now - lastStatsCall < STATS_CALL_INTERVAL) {
    return;
  }
  
  lastStatsCall = now;

  try {
    // Get real system stats from API
    if (window.api && window.api.getSystemStats) {
      const stats = await window.api.getSystemStats()

      if (stats && typeof stats === 'object') {
        systemStats.value = {
          cpuUsage: Math.round((stats.cpuUsage || 0) * 100) / 100,
          memoryUsage: Math.round((stats.memoryUsage || 0) * 100) / 100,
          diskUsage: Math.round((stats.diskUsage || 0) * 100) / 100,
          uptime: formatUptime(stats.uptime || 0),
          totalScripts: stats.totalScripts || 0,
          scheduledTasks: stats.scheduledTasks || 0
        }
        return
      }
    }

    // Fallback: get individual data if main API fails
    const scriptCount = await getScriptCount()
    const taskCount = await getScheduledTaskCount()

    systemStats.value = {
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      uptime: formatUptime(0),
      totalScripts: scriptCount,
      scheduledTasks: taskCount
    }
  } catch (error) {
    console.error('[Frontend] 获取系统统计失败:', error)
    // Keep previous values on error or set defaults
    try {
      if (!systemStats.value.totalScripts) {
        systemStats.value.totalScripts = await getScriptCount()
      }
      if (!systemStats.value.scheduledTasks) {
        systemStats.value.scheduledTasks = await getScheduledTaskCount()
      }
    } catch (fallbackError) {
      console.error('[Frontend] 回退数据获取失败:', fallbackError)
    }
  }
}

// Get actual script count
const getScriptCount = async () => {
  try {
    let totalScripts = 0

    if (window.api) {
      // Get CMD scripts
      const cmdScripts = await window.api.getCmdScripts?.() || []

      // Get JS scripts
      const jsScripts = await window.api.getJsScripts?.() || []

      // Get Python scripts
      const pythonScripts = await window.api.getPythonScripts?.() || []

      totalScripts = cmdScripts.length + jsScripts.length + pythonScripts.length
      console.log(`Script count: CMD(${cmdScripts.length}) + JS(${jsScripts.length}) + Python(${pythonScripts.length}) = ${totalScripts}`)
    }

    return totalScripts
  } catch (error) {
    console.error('Failed to get script count:', error)
    return 0
  }
}

// Get actual scheduled task count
const getScheduledTaskCount = async () => {
  try {
    if (window.api && window.api.getScheduledTasks) {
      const tasks = await window.api.getScheduledTasks()
      const taskCount = Array.isArray(tasks) ? tasks.length : 0
      console.log(`Scheduled tasks count: ${taskCount}`)
      return taskCount
    }
    return 0
  } catch (error) {
    console.error('Failed to get scheduled task count:', error)
    return 0
  }
}

// 格式化运行时间 - 精确到秒
const formatUptime = (uptimeSeconds) => {
  const days = Math.floor(uptimeSeconds / 86400)
  const hours = Math.floor((uptimeSeconds % 86400) / 3600)
  const minutes = Math.floor((uptimeSeconds % 3600) / 60)
  const seconds = Math.floor(uptimeSeconds % 60)

  if (days > 0) {
    return `${days}天${hours}小时${minutes}分${seconds}秒`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分${seconds}秒`
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  } else {
    return `${seconds}秒`
  }
}

// 生命周期
let timeInterval
let statsInterval
let fastStatsInterval

onMounted(async () => {
  updateCurrentTime()
  await getSystemStats()

  // Update time every second
  timeInterval = setInterval(updateCurrentTime, 1000)

  // Update stats every 5 seconds for real-time feel
  statsInterval = setInterval(getSystemStats, 5000)

  // Fast update for CPU/Memory/Uptime every 1 second
  fastStatsInterval = setInterval(async () => {
    try {
      if (window.api && window.api.getSystemStats) {
        const stats = await window.api.getSystemStats()
        // Update CPU, memory, and uptime for fast refresh
        systemStats.value.cpuUsage = Math.round(stats.cpuUsage || systemStats.value.cpuUsage)
        systemStats.value.memoryUsage = Math.round(stats.memoryUsage || systemStats.value.memoryUsage)
        systemStats.value.uptime = formatUptime(stats.uptime || 0)
      }
    } catch (error) {
      console.error('Fast stats update failed:', error)
    }
  }, 1000)

  // Listen for real-time events if available
  setupRealTimeListeners()
})

onUnmounted(() => {
  if (timeInterval) clearInterval(timeInterval)
  if (statsInterval) clearInterval(statsInterval)
  if (fastStatsInterval) clearInterval(fastStatsInterval)

  // Remove event listeners
  removeRealTimeListeners()
})

// Setup real-time event listeners
const setupRealTimeListeners = () => {
  try {
    // Listen for script changes
    if (window.api && window.api.onScriptChange) {
      window.api.onScriptChange(() => {
        // Refresh script count when scripts change
        getScriptCount().then(count => {
          systemStats.value.totalScripts = count
        })
      })
    }

    // Listen for scheduled task changes
    if (window.api && window.api.onScheduledTaskChange) {
      window.api.onScheduledTaskChange(() => {
        // Refresh task count when tasks change
        getScheduledTaskCount().then(count => {
          systemStats.value.scheduledTasks = count
        })
      })
    }

    // Listen for background process events
    if (window.api && window.api.backgroundProcess) {
      window.api.backgroundProcess.onProcessStarted(() => {
        // Trigger a stats refresh when processes start
        getSystemStats()
      })

      window.api.backgroundProcess.onProcessFinished(() => {
        // Trigger a stats refresh when processes finish
        getSystemStats()
      })
    }
  } catch (error) {
    console.error('Failed to setup real-time listeners:', error)
  }
}

// Remove real-time event listeners
const removeRealTimeListeners = () => {
  try {
    if (window.api && window.api.removeAllListeners) {
      window.api.removeAllListeners()
    }

    if (window.api && window.api.backgroundProcess && window.api.backgroundProcess.removeAllListeners) {
      window.api.backgroundProcess.removeAllListeners()
    }
  } catch (error) {
    console.error('Failed to remove real-time listeners:', error)
  }
}
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
  background: var(--background);
  overflow-y: auto;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border);
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.dashboard-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.username {
  font-size: 14px;
  color: var(--text-primary);
}

.icon-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.icon-btn:hover {
  background: var(--background-tertiary);
}

.notification-badge {
  position: relative;
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #f5222d;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-main {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.quick-actions {
  margin-bottom: 32px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.action-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 24px;
}

.action-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.action-info p {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.data-overview {
  margin-bottom: 32px;
}

.stat-category {
  margin-bottom: 32px;
}

.category-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  padding-left: 12px;
  border-left: 4px solid var(--primary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 24px;
  color: white;
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-info p {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.task-management {
  margin-bottom: 32px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-task-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.task-tabs {
  background: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--border);
}

.tab-header {
  display: flex;
  border-bottom: 1px solid var(--border);
}

.tab-btn {
  background: none;
  border: none;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-btn.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

.task-list {
  padding: 16px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border);
  transition: background-color 0.2s;
}

.task-item:hover {
  background: var(--background-tertiary);
}

.task-item:last-child {
  border-bottom: none;
}

.task-item.completed {
  opacity: 0.7;
}

.task-item.completed h4 {
  text-decoration: line-through;
}

.task-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.task-content input[type="checkbox"] {
  margin-top: 4px;
}

.task-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.task-details p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.task-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.task-priority {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.task-priority.high {
  background: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

.task-priority.medium {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.task-priority.low {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.task-date {
  font-size: 12px;
  color: var(--text-secondary);
}

.task-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: var(--background-tertiary);
}

.recent-activities {
  margin-bottom: 32px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: var(--background-secondary);
  border: 1px solid var(--border);
  border-radius: 8px;
}

.activity-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 16px;
  flex-shrink: 0;
}

.activity-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.activity-content p {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: var(--text-secondary);
}

.activity-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: var(--background);
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-content {
  background: var(--background);
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-content.large {
  max-width: 900px;
  width: 95%;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: var(--background-tertiary);
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background: var(--background);
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid var(--border);
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary {
  background: var(--primary);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-hover);
}

.btn-secondary {
  background: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.btn-secondary:hover {
  background: var(--border);
}

.notification-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid var(--border);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--background-tertiary);
}

.notification-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.notification-content p {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: var(--text-secondary);
}

.notification-time {
  font-size: 12px;
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 16px;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .task-actions {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 16px;
  }
  
  .dashboard-title {
    font-size: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}</style>