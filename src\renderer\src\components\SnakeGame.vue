<template>
  <div class="snake-game-container">
    <div class="game-header">
      <h2>🐍 贪吃蛇游戏</h2>
      <div class="game-info">
        <div class="score-info">
          <span class="score">得分: {{ score }}</span>
          <span class="high-score">最高分: {{ highScore }}</span>
          <span class="speed-display">速度: {{ gameSpeed }}ms</span>
        </div>
        <div class="game-controls">
          <button @click="toggleGame" :class="['control-btn', gameState]">
            {{ gameState === 'playing' ? '⏸️ 暂停' : gameState === 'paused' ? '▶️ 继续' : '🎮 开始游戏' }}
          </button>
          <button @click="resetGame" class="control-btn reset">🔄 重置</button>
          <button @click="decreaseSpeed" class="control-btn speed" :disabled="gameSpeed >= 500">⬇️ 减速</button>
          <button @click="increaseSpeed" class="control-btn speed" :disabled="gameSpeed <= 30">⬆️ 加速</button>
          <button @click="toggleAutoMode" :class="['control-btn', 'auto', { active: autoMode }]">
            {{ autoMode ? '🤖 自动模式' : '👤 手动模式' }}
          </button>
        </div>
      </div>
    </div>

    <div class="game-settings">
      <div class="setting-group">
        <label>游戏速度:</label>
        <select v-model="gameSpeed" @change="updateGameSpeed">
          <option value="400">超慢速</option>
          <option value="300">慢速</option>
          <option value="200">中慢速</option>
          <option value="150">中速</option>
          <option value="100">中快速</option>
          <option value="80">快速</option>
          <option value="60">很快</option>
          <option value="40">极速</option>
          <option value="30">超极速</option>
        </select>
      </div>
      <div class="setting-group">
        <label>网格大小:</label>
        <select v-model="gridSize" @change="resetGame">
          <option value="15">精细 (15x15)</option>
          <option value="20">小 (20x20)</option>
          <option value="25">中 (25x25)</option>
          <option value="30">大 (30x30)</option>
        </select>
      </div>
    </div>

    <div class="game-board-container">
      <canvas
        ref="gameCanvas"
        :width="canvasSize"
        :height="canvasSize"
        class="game-canvas"
        @keydown="handleKeyPress"
        tabindex="0"
      ></canvas>

      <div v-if="gameState === 'gameOver'" class="game-over-overlay">
        <div class="game-over-content">
          <h3>🎮 游戏结束</h3>
          <p>最终得分: {{ score }}</p>
          <p v-if="score === highScore" class="new-record">🎉 新纪录！</p>
          <button @click="resetGame" class="restart-btn">再来一局</button>
        </div>
      </div>
    </div>

    <div class="game-instructions">
      <h4>🎮 游戏说明</h4>
      <div class="instructions-grid">
        <div class="instruction-item">
          <span class="key">⬆️⬇️⬅️➡️</span>
          <span class="desc">方向键控制移动</span>
        </div>
        <div class="instruction-item">
          <span class="key">空格</span>
          <span class="desc">暂停/继续游戏</span>
        </div>
        <div class="instruction-item">
          <span class="key">🤖</span>
          <span class="desc">自动模式：AI智能寻路</span>
        </div>
        <div class="instruction-item">
          <span class="key">🍎</span>
          <span class="desc">吃食物增长身体</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// 游戏状态
const gameState = ref('stopped') // 'stopped', 'playing', 'paused', 'gameOver'
const score = ref(0)
const highScore = ref(parseInt(localStorage.getItem('snakeHighScore') || '0'))
const autoMode = ref(true) // 默认开启自动模式
const gameSpeed = ref(50) // 默认极速
const gridSize = ref(15) // 缩小网格尺寸

// 游戏元素
const gameCanvas = ref(null)
const canvasSize = ref(500)
let ctx = null
let gameInterval = null

// 蛇和食物
let snake = [{ x: 10, y: 10 }]
let direction = { x: 0, y: 0 }
let nextDirection = { x: 0, y: 0 }
let food = { x: 15, y: 15 }

// 游戏循环
function gameLoop() {
  if (gameState.value !== 'playing') return

  // 自动模式下使用AI控制
  if (autoMode.value) {
    const aiDirection = findPathToFood()
    if (aiDirection) {
      nextDirection = getDirectionVector(aiDirection)
    }
  }

  // 更新方向（避免反向移动）
  if (nextDirection.x !== 0 || nextDirection.y !== 0) {
    if (!(direction.x === -nextDirection.x && direction.y === -nextDirection.y)) {
      direction = { ...nextDirection }
    }
  }

  // 移动蛇
  const head = { x: snake[0].x + direction.x, y: snake[0].y + direction.y }

  // 检查碰撞
  if (checkCollision(head)) {
    gameOver()
    return
  }

  snake.unshift(head)

  // 检查是否吃到食物
  if (head.x === food.x && head.y === food.y) {
    score.value += 10
    generateFood()

    // 更新最高分
    if (score.value > highScore.value) {
      highScore.value = score.value
      localStorage.setItem('snakeHighScore', highScore.value.toString())
    }
  } else {
    snake.pop()
  }

  draw()
}

// 获取方向向量
function getDirectionVector(directionName) {
  const vectors = {
    'up': { x: 0, y: -1 },
    'down': { x: 0, y: 1 },
    'left': { x: -1, y: 0 },
    'right': { x: 1, y: 0 }
  }
  return vectors[directionName] || { x: 0, y: 0 }
}

// 全新的贪吃蛇AI算法 - 基于Hamilton回路和安全优先策略
function findPathToFood() {
  const head = snake[0]
  const gridCount = Math.floor(canvasSize.value / gridSize.value)

  console.log(`[AI] 当前位置: (${head.x}, ${head.y}), 食物位置: (${food.x}, ${food.y}), 蛇长: ${snake.length}`)

  // 策略1: 如果蛇很短且食物安全可达，使用贪婪策略
  if (snake.length < gridCount * 0.4) {
    const greedyMove = safeGreedyMove(head, food, gridCount)
    if (greedyMove) {
      // 额外检查：确保这个移动不会导致困境
      const vector = getDirectionVector(greedyMove)
      const newPos = { x: head.x + vector.x, y: head.y + vector.y }
      const futureSpace = countNearbyFreeSpaces(newPos, gridCount)

      if (futureSpace >= 8) { // 确保有足够的活动空间
        console.log(`[AI] 使用贪婪策略: ${greedyMove}, 空间: ${futureSpace}`)
        return greedyMove
      }
    }
  }

  // 策略2: 使用Hamilton回路策略确保永不死亡
  const hamiltonMove = hamiltonCycleMove(head, gridCount)
  if (hamiltonMove) {
    console.log(`[AI] 使用Hamilton回路: ${hamiltonMove}`)
    return hamiltonMove
  }

  // 策略3: 紧急情况下的生存策略
  const survivalMove = emergencySurvival(head, gridCount)
  console.log(`[AI] 紧急生存策略: ${survivalMove}`)
  return survivalMove || 'up'
}

// 策略1: 安全的贪婪移动
function safeGreedyMove(head, food, gridCount) {
  const directions = ['up', 'down', 'left', 'right']
  const moves = []

  for (const dir of directions) {
    if (!isSafeMove(dir, gridCount)) continue

    const vector = getDirectionVector(dir)
    const newPos = { x: head.x + vector.x, y: head.y + vector.y }

    // 计算到食物的距离
    const distanceToFood = manhattanDistance(newPos, food)

    // 计算可用空间（简化版本）
    const availableSpace = countNearbyFreeSpaces(newPos, gridCount)

    // 综合评分：距离食物越近越好，可用空间越大越好
    const score = -distanceToFood + availableSpace * 2

    moves.push({ direction: dir, score, distance: distanceToFood, space: availableSpace })
  }

  if (moves.length === 0) return null

  // 排序：优先选择评分最高的移动
  moves.sort((a, b) => b.score - a.score)

  // 如果最佳移动的空间太小，选择空间更大的移动
  if (moves[0].space < 3 && moves.length > 1) {
    const spaciousMove = moves.find(m => m.space >= 5)
    if (spaciousMove) return spaciousMove.direction
  }

  return moves[0].direction
}

// 策略2: Hamilton回路移动（改进版本）
function hamiltonCycleMove(head, gridCount) {
  // 检查是否应该使用Hamilton回路
  const shouldUseHamilton = snake.length > gridCount * 0.5 ||
                           countNearbyFreeSpaces(head, gridCount) < 10

  if (!shouldUseHamilton) {
    return null // 不使用Hamilton回路
  }

  console.log('[AI] 蛇较长或空间紧张，使用Hamilton回路策略')

  // 简化的Hamilton回路：创建一个安全的循环路径
  return createSafeCycle(head, gridCount)
}

// 创建安全的循环路径
function createSafeCycle(head, gridCount) {
  // 尝试跟随蛇的尾巴，形成一个安全的循环
  const tail = snake[snake.length - 1]

  // 计算到尾巴的方向
  const dx = tail.x - head.x
  const dy = tail.y - head.y

  // 选择朝向尾巴的安全方向
  const directions = []

  if (Math.abs(dx) > Math.abs(dy)) {
    // 水平距离更大
    if (dx > 0 && isSafeMove('right', gridCount)) directions.push('right')
    if (dx < 0 && isSafeMove('left', gridCount)) directions.push('left')
    if (dy > 0 && isSafeMove('down', gridCount)) directions.push('down')
    if (dy < 0 && isSafeMove('up', gridCount)) directions.push('up')
  } else {
    // 垂直距离更大
    if (dy > 0 && isSafeMove('down', gridCount)) directions.push('down')
    if (dy < 0 && isSafeMove('up', gridCount)) directions.push('up')
    if (dx > 0 && isSafeMove('right', gridCount)) directions.push('right')
    if (dx < 0 && isSafeMove('left', gridCount)) directions.push('left')
  }

  // 如果有朝向尾巴的安全方向，选择第一个
  if (directions.length > 0) {
    return directions[0]
  }

  // 如果无法朝向尾巴，选择任何安全方向
  const allDirections = ['up', 'down', 'left', 'right']
  for (const dir of allDirections) {
    if (isSafeMove(dir, gridCount)) {
      return dir
    }
  }

  return null
}

// 沿着边界路径移动
function followBorderPath(head, gridCount) {
  const directions = ['up', 'down', 'left', 'right']

  // 定义边界移动的优先级
  if (head.y === 0) { // 顶边
    if (head.x < gridCount - 1 && isSafeMove('right', gridCount)) return 'right'
    if (isSafeMove('down', gridCount)) return 'down'
  }

  if (head.x === gridCount - 1) { // 右边
    if (head.y < gridCount - 1 && isSafeMove('down', gridCount)) return 'down'
    if (isSafeMove('left', gridCount)) return 'left'
  }

  if (head.y === gridCount - 1) { // 底边
    if (head.x > 0 && isSafeMove('left', gridCount)) return 'left'
    if (isSafeMove('up', gridCount)) return 'up'
  }

  if (head.x === 0) { // 左边
    if (head.y > 0 && isSafeMove('up', gridCount)) return 'up'
    if (isSafeMove('right', gridCount)) return 'right'
  }

  // 如果边界路径被阻挡，寻找其他安全移动
  for (const dir of directions) {
    if (isSafeMove(dir, gridCount)) return dir
  }

  return null
}

// 向边界移动
function moveTowardsBorder(head, gridCount) {
  const center = Math.floor(gridCount / 2)
  const directions = []

  // 选择最近的边界方向
  if (head.x < center && isSafeMove('left', gridCount)) directions.push('left')
  if (head.x > center && isSafeMove('right', gridCount)) directions.push('right')
  if (head.y < center && isSafeMove('up', gridCount)) directions.push('up')
  if (head.y > center && isSafeMove('down', gridCount)) directions.push('down')

  if (directions.length > 0) {
    return directions[0]
  }

  // 如果无法向边界移动，选择任何安全方向
  const allDirections = ['up', 'down', 'left', 'right']
  for (const dir of allDirections) {
    if (isSafeMove(dir, gridCount)) return dir
  }

  return null
}

// 策略3: 紧急生存策略
function emergencySurvival(head, gridCount) {
  const directions = ['up', 'down', 'left', 'right']
  const safeMoves = []

  for (const dir of directions) {
    if (isSafeMove(dir, gridCount)) {
      const vector = getDirectionVector(dir)
      const newPos = { x: head.x + vector.x, y: head.y + vector.y }
      const freeSpaces = countNearbyFreeSpaces(newPos, gridCount)

      safeMoves.push({ direction: dir, spaces: freeSpaces })
    }
  }

  if (safeMoves.length === 0) return null

  // 选择周围空间最多的方向
  safeMoves.sort((a, b) => b.spaces - a.spaces)
  return safeMoves[0].direction
}

// 计算附近的自由空间数量
function countNearbyFreeSpaces(pos, gridCount) {
  let count = 0
  const range = 2 // 检查2格范围内的空间

  for (let dx = -range; dx <= range; dx++) {
    for (let dy = -range; dy <= range; dy++) {
      const checkPos = { x: pos.x + dx, y: pos.y + dy }

      if (checkPos.x >= 0 && checkPos.x < gridCount &&
          checkPos.y >= 0 && checkPos.y < gridCount &&
          !isSnakeBody(checkPos)) {
        count++
      }
    }
  }

  return count
}

// 评估当前位置的危险等级
function assessDangerLevel(head, gridCount) {
  let dangerScore = 0

  // 检查周围的危险程度
  const directions = [
    { x: 1, y: 0 }, { x: -1, y: 0 }, { x: 0, y: 1 }, { x: 0, y: -1 }
  ]

  let blockedDirections = 0
  for (const dir of directions) {
    const checkPos = { x: head.x + dir.x, y: head.y + dir.y }
    if (checkPos.x < 0 || checkPos.x >= gridCount ||
        checkPos.y < 0 || checkPos.y >= gridCount ||
        isSnakeBody(checkPos)) {
      blockedDirections++
    }
  }

  dangerScore = blockedDirections / 4

  // 检查可达空间
  const reachableSpaces = countReachableSpaces(head, gridCount)
  const minRequiredSpace = snake.length + 10
  if (reachableSpaces < minRequiredSpace) {
    dangerScore += 0.5
  }

  return Math.min(dangerScore, 1.0)
}

// 寻找安全空间
function findSafeSpace(gridCount) {
  const head = snake[0]
  const directions = ['up', 'down', 'left', 'right']
  const safeOptions = []

  for (const dir of directions) {
    const vector = getDirectionVector(dir)
    const newPos = { x: head.x + vector.x, y: head.y + vector.y }

    if (isSafeMove(dir, gridCount)) {
      const spaceCount = countReachableSpaces(newPos, gridCount)
      const distanceFromCenter = Math.abs(newPos.x - gridCount/2) + Math.abs(newPos.y - gridCount/2)

      safeOptions.push({
        direction: dir,
        spaceCount: spaceCount,
        centerDistance: distanceFromCenter,
        score: spaceCount * 10 - distanceFromCenter
      })
    }
  }

  if (safeOptions.length === 0) return null

  // 选择空间最大的方向
  safeOptions.sort((a, b) => b.score - a.score)
  return safeOptions[0].direction
}

// 智能路径到食物
function findSmartPathToFood(head, food, gridCount) {
  // 使用改进的A*算法，考虑安全性
  const path = findSafePathAStar(head, food, gridCount)

  if (path && path.length > 1) {
    const nextPos = path[1]
    const direction = getDirectionFromPositions(head, nextPos)

    // 模拟吃到食物后的状态
    const futureSnake = [...snake, food]
    const futureReachableSpaces = countReachableSpacesWithSnake(food, gridCount, futureSnake)

    // 确保吃到食物后仍有足够空间
    if (futureReachableSpaces >= futureSnake.length + 5) {
      return direction
    }
  }

  return null
}

// 空间控制策略
function executeSpaceControlStrategy(gridCount) {
  const head = snake[0]
  const tail = snake[snake.length - 1]

  // 优先跟随尾巴，保持空间连通性
  const pathToTail = findSafePathAStar(head, tail, gridCount)

  if (pathToTail && pathToTail.length > 1) {
    const nextPos = pathToTail[1]
    const direction = getDirectionFromPositions(head, nextPos)

    if (isSafeMove(direction, gridCount)) {
      return direction
    }
  }

  // 如果无法跟随尾巴，寻找最大空间方向
  return findSafeSpace(gridCount)
}

// 安全的A*寻路算法
function findSafePathAStar(start, goal, gridCount) {
  const openSet = [{ ...start, g: 0, h: manhattanDistance(start, goal), f: manhattanDistance(start, goal), parent: null }]
  const closedSet = new Set()
  
  while (openSet.length > 0) {
    // 找到f值最小的节点
    openSet.sort((a, b) => a.f - b.f)
    const current = openSet.shift()
    
    // 到达目标
    if (current.x === goal.x && current.y === goal.y) {
      const path = []
      let node = current
      while (node) {
        path.unshift({ x: node.x, y: node.y })
        node = node.parent
      }
      return path
    }
    
    closedSet.add(`${current.x},${current.y}`)
    
    // 检查四个方向的邻居
    const neighbors = [
      { x: current.x + 1, y: current.y },
      { x: current.x - 1, y: current.y },
      { x: current.x, y: current.y + 1 },
      { x: current.x, y: current.y - 1 }
    ]
    
    for (const neighbor of neighbors) {
      // 检查边界和蛇身碰撞
      if (neighbor.x < 0 || neighbor.x >= gridCount || 
          neighbor.y < 0 || neighbor.y >= gridCount ||
          isSnakeBody(neighbor) ||
          closedSet.has(`${neighbor.x},${neighbor.y}`)) {
        continue
      }
      
      // 添加安全性检查
      const safetyScore = calculatePositionSafety(neighbor, gridCount)
      if (safetyScore < 0.2) continue // 跳过不安全的位置

      const g = current.g + 1
      const h = manhattanDistance(neighbor, goal)
      // 将安全性纳入评分，安全的位置优先级更高
      const f = g + h - (safetyScore * 3)
      
      const existingNode = openSet.find(node => node.x === neighbor.x && node.y === neighbor.y)
      if (!existingNode || g < existingNode.g) {
        if (existingNode) {
          existingNode.g = g
          existingNode.f = f
          existingNode.parent = current
        } else {
          openSet.push({ ...neighbor, g, h, f, parent: current })
        }
      }
    }
  }
  
  return null // 无路径
}

// 曼哈顿距离
function manhattanDistance(a, b) {
  return Math.abs(a.x - b.x) + Math.abs(a.y - b.y)
}

// 计算位置的安全性评分
function calculatePositionSafety(pos, gridCount) {
  let safetyScore = 1.0

  // 1. 检查周围的开放空间
  const directions = [
    { x: 1, y: 0 }, { x: -1, y: 0 }, { x: 0, y: 1 }, { x: 0, y: -1 }
  ]

  let openSpaces = 0
  for (const dir of directions) {
    const checkPos = { x: pos.x + dir.x, y: pos.y + dir.y }
    if (checkPos.x >= 0 && checkPos.x < gridCount &&
        checkPos.y >= 0 && checkPos.y < gridCount &&
        !isSnakeBody(checkPos)) {
      openSpaces++
    }
  }

  safetyScore *= (openSpaces / 4)

  // 2. 距离边界的距离（中心区域更安全）
  const distanceFromBorder = Math.min(pos.x, pos.y, gridCount - 1 - pos.x, gridCount - 1 - pos.y)
  safetyScore *= Math.min(distanceFromBorder / 3, 1.0)

  // 3. 可达空间大小
  const reachableSpaces = Math.min(countReachableSpaces(pos, gridCount), 50)
  const minRequiredSpace = snake.length + 5
  if (reachableSpaces < minRequiredSpace) {
    safetyScore *= 0.1 // 严重惩罚空间不足的位置
  } else {
    safetyScore *= Math.min(reachableSpaces / minRequiredSpace, 2.0)
  }

  return Math.max(safetyScore, 0.0)
}

// 检查是否是蛇身
function isSnakeBody(pos) {
  return snake.some((segment, index) => 
    segment.x === pos.x && segment.y === pos.y && index < snake.length - 1
  )
}

// 根据两个位置获取方向
function getDirectionFromPositions(from, to) {
  const dx = to.x - from.x
  const dy = to.y - from.y
  
  if (dx === 1) return 'right'
  if (dx === -1) return 'left'
  if (dy === 1) return 'down'
  if (dy === -1) return 'up'
  return null
}

// 检查移动是否安全
function isSafeMove(dir, gridCount) {
  const vector = getDirectionVector(dir)
  const newHead = { x: snake[0].x + vector.x, y: snake[0].y + vector.y }

  // 检查边界
  if (newHead.x < 0 || newHead.x >= gridCount || newHead.y < 0 || newHead.y >= gridCount) {
    return false
  }

  // 检查蛇身碰撞（除了尾部，因为尾部会移动）
  for (let i = 0; i < snake.length - 1; i++) {
    if (snake[i].x === newHead.x && snake[i].y === newHead.y) {
      return false
    }
  }

  // 检查是否反向移动
  const currentDir = getCurrentDirection()
  const opposites = { 'up': 'down', 'down': 'up', 'left': 'right', 'right': 'left' }
  return dir !== opposites[currentDir]
}

// 检查是否有逃生路径 - 改进版本
function hasEscapeRoute(pos, gridCount) {
  // 使用洪水填充算法检查可达空间
  const visited = new Set()
  const queue = [pos]
  let reachableSpaces = 0

  while (queue.length > 0 && reachableSpaces < snake.length + 5) {
    const current = queue.shift()
    const key = `${current.x},${current.y}`

    if (visited.has(key)) continue
    visited.add(key)
    reachableSpaces++

    const directions = [
      { x: current.x + 1, y: current.y },
      { x: current.x - 1, y: current.y },
      { x: current.x, y: current.y + 1 },
      { x: current.x, y: current.y - 1 }
    ]

    for (const next of directions) {
      if (next.x >= 0 && next.x < gridCount &&
          next.y >= 0 && next.y < gridCount &&
          !isSnakeBody(next) &&
          !visited.has(`${next.x},${next.y}`)) {
        queue.push(next)
      }
    }
  }

  // 确保有足够的空间容纳蛇的长度
  return reachableSpaces >= snake.length + 3
}

// 寻找最安全的方向
function findSafestDirection(gridCount) {
  const directions = ['up', 'down', 'left', 'right']
  const safeDirections = []
  
  for (const dir of directions) {
    if (isSafeMove(dir, gridCount)) {
      const vector = getDirectionVector(dir)
      const newPos = { x: snake[0].x + vector.x, y: snake[0].y + vector.y }
      
      // 计算安全性评分
      const safetyScore = calculateSafetyScore(newPos, gridCount)
      safeDirections.push({ direction: dir, score: safetyScore })
    }
  }
  
  if (safeDirections.length === 0) return null
  
  // 选择安全性评分最高的方向
  safeDirections.sort((a, b) => b.score - a.score)
  return safeDirections[0].direction
}

// 计算位置的安全性评分 - 改进版本
function calculateSafetyScore(pos, gridCount) {
  let score = 0

  // 1. 距离边界的距离（越远越安全）
  const distanceFromBorder = Math.min(pos.x, pos.y, gridCount - 1 - pos.x, gridCount - 1 - pos.y)
  score += distanceFromBorder * 15

  // 2. 使用洪水填充算法计算可达空间
  const reachableSpaces = countReachableSpaces(pos, gridCount)
  score += reachableSpaces * 8

  // 3. 避免靠近蛇头附近的危险区域
  const head = snake[0]
  const distanceFromHead = manhattanDistance(pos, head)
  if (distanceFromHead <= 2) {
    score -= 50 // 严重惩罚靠近蛇头的位置
  }

  // 4. 距离蛇尾的距离（跟随尾部策略，但不要太近）
  const tail = snake[snake.length - 1]
  const distanceToTail = manhattanDistance(pos, tail)
  if (distanceToTail > 1 && distanceToTail <= 4) {
    score += distanceToTail * 5
  }

  // 5. 检查前方几步是否安全
  const futureSteps = checkFutureSafety(pos, gridCount, 3)
  score += futureSteps * 10

  // 6. 距离食物的距离（适度考虑）
  const distanceToFood = manhattanDistance(pos, food)
  score += Math.max(0, 10 - distanceToFood) * 2

  return score
}

// 计算从指定位置可达的空间数量
function countReachableSpaces(startPos, gridCount) {
  const visited = new Set()
  const queue = [startPos]
  let count = 0

  while (queue.length > 0 && count < 50) { // 限制搜索范围
    const current = queue.shift()
    const key = `${current.x},${current.y}`

    if (visited.has(key)) continue
    visited.add(key)
    count++

    const neighbors = [
      { x: current.x + 1, y: current.y },
      { x: current.x - 1, y: current.y },
      { x: current.x, y: current.y + 1 },
      { x: current.x, y: current.y - 1 }
    ]

    for (const neighbor of neighbors) {
      if (neighbor.x >= 0 && neighbor.x < gridCount &&
          neighbor.y >= 0 && neighbor.y < gridCount &&
          !isSnakeBody(neighbor) &&
          !visited.has(`${neighbor.x},${neighbor.y}`)) {
        queue.push(neighbor)
      }
    }
  }

  return count
}

// 计算指定蛇身状态下的可达空间
function countReachableSpacesWithSnake(startPos, gridCount, snakeBody) {
  const visited = new Set()
  const queue = [startPos]
  let count = 0

  while (queue.length > 0 && count < 100) {
    const current = queue.shift()
    const key = `${current.x},${current.y}`

    if (visited.has(key)) continue
    visited.add(key)
    count++

    const neighbors = [
      { x: current.x + 1, y: current.y },
      { x: current.x - 1, y: current.y },
      { x: current.x, y: current.y + 1 },
      { x: current.x, y: current.y - 1 }
    ]

    for (const neighbor of neighbors) {
      if (neighbor.x >= 0 && neighbor.x < gridCount &&
          neighbor.y >= 0 && neighbor.y < gridCount &&
          !isPositionInSnake(neighbor, snakeBody) &&
          !visited.has(`${neighbor.x},${neighbor.y}`)) {
        queue.push(neighbor)
      }
    }
  }

  return count
}

// 检查位置是否在指定蛇身中
function isPositionInSnake(pos, snakeBody) {
  return snakeBody.some(segment => segment.x === pos.x && segment.y === pos.y)
}

// 检查未来几步的安全性
function checkFutureSafety(pos, gridCount, steps) {
  let safeSteps = 0
  const directions = [
    { x: 1, y: 0 }, { x: -1, y: 0 }, { x: 0, y: 1 }, { x: 0, y: -1 }
  ]

  for (const dir of directions) {
    let currentPos = { ...pos }
    let stepCount = 0

    for (let i = 0; i < steps; i++) {
      currentPos = { x: currentPos.x + dir.x, y: currentPos.y + dir.y }

      if (currentPos.x >= 0 && currentPos.x < gridCount &&
          currentPos.y >= 0 && currentPos.y < gridCount &&
          !isSnakeBody(currentPos)) {
        stepCount++
      } else {
        break
      }
    }

    safeSteps += stepCount
  }

  return safeSteps
}

// 获取当前方向
function getCurrentDirection() {
  if (direction.x === 0 && direction.y === -1) return 'up'
  if (direction.x === 0 && direction.y === 1) return 'down'
  if (direction.x === -1 && direction.y === 0) return 'left'
  if (direction.x === 1 && direction.y === 0) return 'right'
  return null
}

// 检查碰撞
function checkCollision(head) {
  const gridCount = Math.floor(canvasSize.value / gridSize.value)

  // 检查边界碰撞
  if (head.x < 0 || head.x >= gridCount || head.y < 0 || head.y >= gridCount) {
    return true
  }

  // 检查自身碰撞
  for (const segment of snake) {
    if (head.x === segment.x && head.y === segment.y) {
      return true
    }
  }

  return false
}

// 生成食物
function generateFood() {
  const gridCount = Math.floor(canvasSize.value / gridSize.value)
  let newFood

  do {
    newFood = {
      x: Math.floor(Math.random() * gridCount),
      y: Math.floor(Math.random() * gridCount)
    }
  } while (snake.some(segment => segment.x === newFood.x && segment.y === newFood.y))

  food = newFood
}

// 绘制游戏
function draw() {
  if (!ctx) return

  // 清空画布
  ctx.fillStyle = '#1a1a1a'
  ctx.fillRect(0, 0, canvasSize.value, canvasSize.value)

  // 绘制网格
  ctx.strokeStyle = '#333'
  ctx.lineWidth = 1
  for (let i = 0; i <= canvasSize.value; i += gridSize.value) {
    ctx.beginPath()
    ctx.moveTo(i, 0)
    ctx.lineTo(i, canvasSize.value)
    ctx.stroke()

    ctx.beginPath()
    ctx.moveTo(0, i)
    ctx.lineTo(canvasSize.value, i)
    ctx.stroke()
  }

  // 绘制蛇
  snake.forEach((segment, index) => {
    ctx.fillStyle = index === 0 ? '#4CAF50' : '#81C784'
    ctx.fillRect(
      segment.x * gridSize.value + 1,
      segment.y * gridSize.value + 1,
      gridSize.value - 2,
      gridSize.value - 2
    )
  })

  // 绘制食物
  ctx.fillStyle = '#F44336'
  ctx.fillRect(
    food.x * gridSize.value + 1,
    food.y * gridSize.value + 1,
    gridSize.value - 2,
    gridSize.value - 2
  )
}

// 游戏控制
function toggleGame() {
  if (gameState.value === 'stopped' || gameState.value === 'gameOver') {
    startGame()
  } else if (gameState.value === 'playing') {
    pauseGame()
  } else if (gameState.value === 'paused') {
    resumeGame()
  }
}

function startGame() {
  resetGameData()
  gameState.value = 'playing'
  gameInterval = setInterval(gameLoop, gameSpeed.value)
}

function pauseGame() {
  gameState.value = 'paused'
  if (gameInterval) {
    clearInterval(gameInterval)
    gameInterval = null
  }
}

function resumeGame() {
  gameState.value = 'playing'
  gameInterval = setInterval(gameLoop, gameSpeed.value)
}

function resetGame() {
  if (gameInterval) {
    clearInterval(gameInterval)
    gameInterval = null
  }
  gameState.value = 'stopped'
  resetGameData()
  draw()
}

function resetGameData() {
  snake = [{ x: 10, y: 10 }]
  direction = { x: 0, y: 0 }
  nextDirection = { x: 0, y: 0 }
  score.value = 0
  generateFood()
}

function gameOver() {
  gameState.value = 'gameOver'
  if (gameInterval) {
    clearInterval(gameInterval)
    gameInterval = null
  }
}

function toggleAutoMode() {
  autoMode.value = !autoMode.value
}

function updateGameSpeed() {
  if (gameInterval && gameState.value === 'playing') {
    clearInterval(gameInterval)
    gameInterval = setInterval(gameLoop, gameSpeed.value)
  }
}

function increaseSpeed() {
  if (gameSpeed.value > 30) {
    // 根据当前速度动态调整加速幅度
    let speedDecrease = 10
    if (gameSpeed.value > 200) speedDecrease = 20
    else if (gameSpeed.value > 100) speedDecrease = 15
    else if (gameSpeed.value > 60) speedDecrease = 10
    else speedDecrease = 5

    gameSpeed.value = Math.max(30, gameSpeed.value - speedDecrease)
    updateGameSpeed()
    console.log(`[SnakeGame] 加速到: ${gameSpeed.value}ms`)
  }
}

function decreaseSpeed() {
  if (gameSpeed.value < 500) {
    // 根据当前速度动态调整减速幅度
    let speedIncrease = 10
    if (gameSpeed.value < 60) speedIncrease = 5
    else if (gameSpeed.value < 100) speedIncrease = 10
    else if (gameSpeed.value < 200) speedIncrease = 15
    else speedIncrease = 20

    gameSpeed.value = Math.min(500, gameSpeed.value + speedIncrease)
    updateGameSpeed()
    console.log(`[SnakeGame] 减速到: ${gameSpeed.value}ms`)
  }
}

// 键盘控制
function handleKeyPress(event) {
  if (gameState.value !== 'playing' || autoMode.value) return

  switch (event.key) {
    case 'ArrowUp':
      nextDirection = { x: 0, y: -1 }
      break
    case 'ArrowDown':
      nextDirection = { x: 0, y: 1 }
      break
    case 'ArrowLeft':
      nextDirection = { x: -1, y: 0 }
      break
    case 'ArrowRight':
      nextDirection = { x: 1, y: 0 }
      break
    case ' ':
      event.preventDefault()
      toggleGame()
      break
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()
  if (gameCanvas.value) {
    ctx = gameCanvas.value.getContext('2d')
    resetGameData()
    draw()

    // 聚焦画布以接收键盘事件
    gameCanvas.value.focus()
  }
})

onUnmounted(() => {
  if (gameInterval) {
    clearInterval(gameInterval)
  }
})
</script>

<style scoped>
.snake-game-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background: var(--background-primary);
  color: var(--text-primary);
}

.game-header {
  text-align: center;
  margin-bottom: 20px;
}

.game-header h2 {
  margin: 0 0 15px 0;
  color: var(--primary);
}

.game-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.score-info {
  display: flex;
  gap: 20px;
  font-weight: 600;
  align-items: center;
  flex-wrap: wrap;
}

.score {
  color: var(--success);
}

.high-score {
  color: var(--warning);
}

.speed-display {
  color: var(--info);
  font-size: 14px;
}

.game-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.control-btn.stopped,
.control-btn.gameOver {
  background: var(--success);
  color: white;
}

.control-btn.playing {
  background: var(--warning);
  color: white;
}

.control-btn.paused {
  background: var(--primary);
  color: white;
}

.control-btn.reset {
  background: var(--danger);
  color: white;
}

.control-btn.auto {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border);
}

.control-btn.auto.active {
  background: var(--info);
  color: white;
  border-color: var(--info);
}

.control-btn.speed {
  background: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border);
  font-size: 12px;
  padding: 6px 12px;
}

.control-btn.speed:hover:not(:disabled) {
  background: var(--primary);
  color: white;
}

.control-btn.speed:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn:hover:not(:disabled) {
  opacity: 0.8;
  transform: translateY(-1px);
}

.game-settings {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group label {
  font-weight: 500;
  color: var(--text-secondary);
}

.setting-group select {
  padding: 5px 10px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background: var(--background-secondary);
  color: var(--text-primary);
}

.game-board-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.game-canvas {
  border: 2px solid var(--border);
  border-radius: 8px;
  background: #1a1a1a;
  cursor: pointer;
}

.game-canvas:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.game-over-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.game-over-content {
  background: var(--background-secondary);
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  border: 2px solid var(--border);
}

.game-over-content h3 {
  margin: 0 0 15px 0;
  color: var(--danger);
}

.game-over-content p {
  margin: 10px 0;
  font-size: 16px;
}

.new-record {
  color: var(--warning);
  font-weight: 600;
}

.restart-btn {
  margin-top: 15px;
  padding: 10px 20px;
  background: var(--success);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.restart-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.game-instructions {
  background: var(--background-secondary);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid var(--border);
}

.game-instructions h4 {
  margin: 0 0 15px 0;
  color: var(--primary);
}

.instructions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.instruction-item .key {
  background: var(--background-tertiary);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  min-width: 60px;
  text-align: center;
  border: 1px solid var(--border);
}

.instruction-item .desc {
  color: var(--text-secondary);
  font-size: 14px;
}

@media (max-width: 768px) {
  .game-info {
    flex-direction: column;
    text-align: center;
  }

  .game-settings {
    flex-direction: column;
    align-items: center;
  }

  .instructions-grid {
    grid-template-columns: 1fr;
  }
}
</style>