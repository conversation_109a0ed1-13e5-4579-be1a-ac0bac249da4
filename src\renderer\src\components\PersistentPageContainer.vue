<template>
  <div class="persistent-page-container">
    <!-- 页面实例容器 -->
    <div 
      v-for="instance in pageInstances" 
      :key="instance.id"
      :class="['page-instance', { 
        'active': instance.isActive, 
        'background': !instance.isActive && instance.isLoaded 
      }]"
      :style="{ display: instance.isActive ? 'block' : 'none' }"
    >
      <component 
        :is="instance.component" 
        v-if="instance.isLoaded"
        :ref="(el) => setPageRef(instance.id, el)"
        :page-id="instance.id"
        :page-state="instance.state"
        @update-state="(update) => updatePageState(instance.id, update)"
        @navigate-to="handleNavigateTo"
      />
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载页面...</p>
    </div>

    <!-- 调试信息 (开发模式) -->
    <div v-if="showDebugInfo" class="debug-info">
      <h4>页面状态调试</h4>
      <div class="debug-stats">
        <div>总页面: {{ stats.totalPages }}</div>
        <div>活动页面: {{ stats.activePages }}</div>
        <div>后台页面: {{ stats.backgroundPages }}</div>
        <div>内存使用: {{ stats.memoryUsage.estimated }}</div>
      </div>
      <div class="debug-pages">
        <div v-for="instance in pageInstances" :key="instance.id" class="debug-page">
          <span :class="['status', instance.isActive ? 'active' : 'background']">
            {{ instance.isActive ? '●' : '○' }}
          </span>
          {{ instance.config.title }} ({{ instance.id }})
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, markRaw, watch } from 'vue'
import { pageStateManager } from '../utils/pageStateManager.js'

// 组件映射
import CmdScripts from './CmdScripts.vue'
import PythonScripts from './PythonScripts.vue'
import JsScripts from './JsScripts.vue'
import ToolsWorkspace from './ToolsWorkspace.vue'
import ScheduledTasks from './ScheduledTasks.vue'
import Settings from './Settings.vue'
import DashboardHome from './DashboardHome.vue'
import EasyVoiceWebTTS from './EasyVoiceWebTTS.vue'
import VoiceCloning from './VoiceCloning.vue'
import SnakeGame from './SnakeGame.vue'

// 将 Workbench 映射到 DashboardHome
const Workbench = DashboardHome

const componentMap = {
  CmdScripts: markRaw(CmdScripts),
  PythonScripts: markRaw(PythonScripts),
  JsScripts: markRaw(JsScripts),
  ToolsWorkspace: markRaw(ToolsWorkspace),
  ScheduledTasks: markRaw(ScheduledTasks),
  Settings: markRaw(Settings),
  Workbench: markRaw(Workbench),
  // 工具页面组件
  EasyVoiceWebTTS: markRaw(EasyVoiceWebTTS),
  VoiceCloning: markRaw(VoiceCloning),
  SnakeGame: markRaw(SnakeGame)
}

// Props
const props = defineProps({
  currentPage: {
    type: String,
    required: true
  },
  showDebugInfo: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['navigate-to', 'page-loaded', 'page-error'])

// 响应式数据
const isLoading = ref(false)
const pageRefs = ref(new Map())

// 计算属性
const pageInstances = computed(() => {
  return Array.from(pageStateManager.pageInstances.values())
})

const stats = computed(() => {
  return pageStateManager.getStats()
})

// 方法
const setPageRef = (pageId, el) => {
  if (el) {
    pageRefs.value.set(pageId, el)
  }
}

const updatePageState = (pageId, update) => {
  pageStateManager.updatePageState(pageId, update)
}

const handleNavigateTo = (pageId) => {
  emit('navigate-to', pageId)
}

/**
 * 加载页面
 */
const loadPage = async (pageId) => {
  console.log(`[PersistentPageContainer] 开始加载页面: ${pageId}`)
  console.log(`[PersistentPageContainer] 组件映射:`, Object.keys(componentMap))
  
  if (!componentMap[pageId]) {
    console.warn(`[PersistentPageContainer] 未找到组件: ${pageId}`)
    emit('page-error', { pageId, error: '组件不存在' })
    return
  }

  console.log(`[PersistentPageContainer] 找到组件: ${pageId}`)
  isLoading.value = true

  try {
    // 获取或创建页面实例
    const instance = await pageStateManager.getPageInstance(pageId, async () => {
      console.log(`[PersistentPageContainer] 创建组件实例: ${pageId}`)
      return componentMap[pageId]
    })

    console.log(`[PersistentPageContainer] 页面实例:`, instance)

    if (instance) {
      // 激活页面
      pageStateManager.activatePage(pageId)
      console.log(`[PersistentPageContainer] 页面已激活: ${pageId}`)
      
      // 等待下一帧后恢复滚动位置
      await nextTick()
      setTimeout(() => {
        restorePageScrollPosition(pageId)
      }, 100)

      emit('page-loaded', { pageId, instance })
    }
  } catch (error) {
    console.error(`[PersistentPageContainer] 加载页面失败: ${pageId}`, error)
    emit('page-error', { pageId, error })
  } finally {
    isLoading.value = false
  }
}

/**
 * 保存页面滚动位置
 */
const savePageScrollPosition = (pageId) => {
  const pageRef = pageRefs.value.get(pageId)
  if (pageRef && pageRef.$el) {
    const scrollElement = pageRef.$el.querySelector('.scrollable') || pageRef.$el
    pageStateManager.saveScrollPosition(pageId, scrollElement)
  }
}

/**
 * 恢复页面滚动位置
 */
const restorePageScrollPosition = (pageId) => {
  const pageRef = pageRefs.value.get(pageId)
  if (pageRef && pageRef.$el) {
    const scrollElement = pageRef.$el.querySelector('.scrollable') || pageRef.$el
    pageStateManager.restoreScrollPosition(pageId, scrollElement)
  }
}

/**
 * 切换页面
 */
const switchToPage = async (pageId) => {
  // 保存当前页面的滚动位置
  const currentPageId = pageStateManager.activePage.value
  if (currentPageId) {
    savePageScrollPosition(currentPageId)
  }

  // 加载新页面
  await loadPage(pageId)
}

// 监听页面切换
watch(() => props.currentPage, async (newPageId, oldPageId) => {
  if (newPageId !== oldPageId) {
    console.log(`[PersistentPageContainer] 页面切换: ${oldPageId} -> ${newPageId}`)
    await switchToPage(newPageId)
  }
}, { immediate: true })

// 生命周期
onMounted(async () => {
  // 初始化页面状态管理器
  pageStateManager.initialize()
  
  // 加载当前页面
  if (props.currentPage) {
    await loadPage(props.currentPage)
  }

  // 设置定期清理
  const cleanupInterval = setInterval(() => {
    pageStateManager.cleanupExpiredStates()
  }, 60 * 60 * 1000) // 每小时清理一次

  // 保存清理定时器引用
  onUnmounted(() => {
    clearInterval(cleanupInterval)
  })
})

onUnmounted(() => {
  // 保存所有页面的滚动位置
  pageRefs.value.forEach((pageRef, pageId) => {
    savePageScrollPosition(pageId)
  })
  
  // 保存状态
  pageStateManager.savePersistedState()
})

// 暴露方法给父组件
defineExpose({
  loadPage,
  switchToPage,
  getPageInstance: (pageId) => pageStateManager.pageInstances.get(pageId),
  getStats: () => pageStateManager.getStats(),
  cleanup: () => pageStateManager.cleanupExpiredStates(),
  reset: () => pageStateManager.reset()
})
</script>

<style scoped>
.persistent-page-container {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.page-instance {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: var(--background);
  transition: opacity 0.2s ease;
}

.page-instance.active {
  z-index: 10;
  opacity: 1;
}

.page-instance.background {
  z-index: 1;
  opacity: 0;
  pointer-events: none;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.debug-info {
  position: fixed;
  top: 10px;
  right: 10px;
  background: var(--card-background);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 12px;
  font-size: 12px;
  z-index: 1000;
  max-width: 250px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.debug-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--text-primary);
}

.debug-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
  margin-bottom: 8px;
  font-size: 11px;
  color: var(--text-secondary);
}

.debug-pages {
  border-top: 1px solid var(--border);
  padding-top: 8px;
}

.debug-page {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 2px;
  font-size: 11px;
  color: var(--text-secondary);
}

.status {
  font-size: 8px;
  width: 8px;
  text-align: center;
}

.status.active {
  color: var(--success);
}

.status.background {
  color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .debug-info {
    top: auto;
    bottom: 10px;
    right: 10px;
    max-width: 200px;
  }
}
</style>